﻿
using MyBya.DataAccess.Interfaces;
using MyBya.Models.TestResultHeartRate;
using MyBya.Entities;
using MyBya.DbContext.Handler.Interface;

namespace MyBya.DataAccess
{
    public class TestResultHeartRateRepository : BaseRepository<TestResultHeartRatesEntity>, ITestResultHeartRateRepository
    {
		public TestResultHeartRateRepository(IDatabaseHandler<TestResultHeartRatesEntity> databaseHandler)
			: base(databaseHandler)
		{
			
        }
    }
}