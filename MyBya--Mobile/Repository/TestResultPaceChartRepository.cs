﻿using System;
using AutoMapper;
using MyBya.DataAccess.Interfaces;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models.TestResultPaceChart;

namespace MyBya.DataAccess
{
	public class TestResultPaceChartRepository : BaseRepository<TestResultPaceChartEntity>, ITestResultPaceChartRepository
	{
		public TestResultPaceChartRepository(IDatabaseHandler<TestResultPaceChartEntity> databaseHandler) 
			: base(databaseHandler)
		{
			
		}
		
        public async Task<List<TestResultPaceChartModel>> GetPacesByInterval(string interval_distance)
		{
			string query = @$"SELECT *
                                FROM test_result_pace_chart
                               WHERE System = '{interval_distance}m-Min'
                                  OR System = '{interval_distance}m-Max'";

			var result = await _databaseHandler.ExecuteQuery(query);

			var models = new List<TestResultPaceChartModel>();

			foreach (var entity in result)
			{
				var model = ServiceHelper
					.GetService<IMapper>()
					.Map<TestResultPaceChartModel>(entity);

				models.Add(model);
			}

			return models;
		}
	}
}