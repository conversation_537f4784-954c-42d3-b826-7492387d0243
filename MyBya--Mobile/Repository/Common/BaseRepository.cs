using System.Linq.Expressions;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities.Common;

namespace MyBya.DataAccess;

public class BaseRepository<E> where E : BaseEntity, new()
{
    protected IDatabaseHandler<E> _databaseHandler;
    
    public BaseRepository(IDatabaseHandler<E> databaseHandler)
    {
        _databaseHandler = databaseHandler;
    }

    public virtual async Task Delete(E entity)
    {
        await _databaseHandler.Delete(entity);
    }

    public virtual async Task Insert(E entity)
    {
        await _databaseHandler.Insert(entity);
    }

    public virtual async Task InsertAll(List<E> entities)
    {
        await _databaseHandler.InsertAll(entities);
    }

    public virtual async Task<IList<E>> SelectAllItems()
    {
        return await _databaseHandler.SelectAllItems();
    }

    public virtual async Task<IList<E>> SelectByCriteria(Expression<Func<E, bool>> predicate)
    {
        return await _databaseHandler.SelectByCriteria(predicate);
    }

    public virtual async Task Update(E entity)
    {
        await _databaseHandler.Update(entity);
    }

    public virtual async Task Clear()
    {
        await _databaseHandler.Clear();
    }
}