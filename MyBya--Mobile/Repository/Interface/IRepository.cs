using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MyBya.Entities.Common;

namespace MyBya.DataAccess.Interfaces
{
    public interface IRepository<E> where E : BaseEntity
    {   
        Task Delete(E entity);
        Task Insert(E entity);
        Task InsertAll(List<E> entities);
        Task<IList<E>> SelectAllItems();
        Task<IList<E>> SelectByCriteria(Expression<Func<E, bool>> predicate);
        Task Update(E entity);
        Task Clear();
    }
}