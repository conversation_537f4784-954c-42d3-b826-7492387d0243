using System.Windows.Input;
using MyBya.Helpers;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.Controls;

public class SyncedCommand : ICommand
{
    private Func<object, Task> Exec {get; set;}
    private bool ShowLoading {get; set;}

    public SyncedCommand(Func<object, Task> execute, bool showLoading = false)
    {
        Exec = async (obj) => await execute(obj);
        ShowLoading = showLoading;
    }

    public SyncedCommand(Func<Task> execute, bool showLoading = false)
    {
        Exec = async (obj) => await execute();
        ShowLoading = showLoading;
    }

    public SyncedCommand(Action execute, bool showLoading = false)
    {
        Exec = async (obj) => execute();
        ShowLoading = showLoading;
    }

    public SyncedCommand(Action<object> execute, bool showLoading = false)
    {
        Exec = async (obj) => execute(obj);
        ShowLoading = showLoading;
    }

    public event EventHandler? CanExecuteChanged;

    public bool CanExecute(object? parameter)
    {
        return true;
    }

    public async void Execute(object? parameter)
    {
        try 
        {
            ViewModelBase viewModel = GetViewModel();

            if (ShowLoading)
                viewModel.IsBusy = true;

            await Exec(parameter);

            if (ShowLoading)
                viewModel.IsBusy = false;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            ViewModelBase viewModel = GetViewModel();
            await viewModel.ShowMessageError();
            viewModel.IsBusy = false;
        }
    }

    private ViewModelBase GetViewModel() 
    {
        Page? page = NavigationHelper.GetCurrentPage();
        return (ViewModelBase)page.BindingContext;
    }
}
