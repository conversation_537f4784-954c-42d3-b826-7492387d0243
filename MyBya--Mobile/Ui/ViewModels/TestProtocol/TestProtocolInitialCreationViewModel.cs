﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Maui.Core.Extensions;
using MyBya.Constants;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models.TrainingPlanEvent;
using MyBya.Parameters;
using MyBya.Services.TrainingPlanEvent;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol
{
	public class TestProtocolInitialCreationViewModel : ViewModelBase
	{
		private List<TrainingPlanEventModel> trainingPlanEvents { get; set; }

		private ObservableCollection<string> eventList;
		public ObservableCollection<string> EventList
		{
			get => eventList;
			set => SetProperty(ref eventList, value);
		}

		private SportEnum? selectedSport;
		public SportEnum? SelectedSport
		{
			get => selectedSport;
			set => SetProperty(ref selectedSport, value);
		}

		private bool isNextButtonEnabled;
		public bool IsNextButtonEnabled
		{
			get => isNextButtonEnabled;
			set => SetProperty(ref isNextButtonEnabled, value);
		}

		private LevelEnum? selectedLevel;
		public LevelEnum? SelectedLevel
		{
			get => selectedLevel;
			set => SetProperty(ref selectedLevel, value);
		}

		private string? selectedEvent;
		public string? SelectedEvent
		{
			get => selectedEvent;
			set => SetProperty(ref selectedEvent, value);
		}

		private bool isSelectedSportNotEmpty;
		public bool IsSelectedSportNotEmpty
		{
			get => isSelectedSportNotEmpty;
			set => SetProperty(ref isSelectedSportNotEmpty, value);
		}

		public ICommand NextPhaseCommand { get; set; }

		public TestProtocolInitialCreationViewModel()
		{
			IsSelectedSportNotEmpty = false;
			EventList = new ObservableCollection<string>();
			NextPhaseCommand = new SyncedCommand(NextPhase, showLoading: true);
		}

		private async Task NextPhase()
		{
			IsSelectedSportNotEmpty = SelectedSport != null;
			IsNextButtonEnabled = false;

			int? eventId = trainingPlanEvents
				.FirstOrDefault(x => x.EventName == SelectedEvent)?.Id;

			if (SelectedSport != null &&
				SelectedLevel != null &&
				!string.IsNullOrEmpty(SelectedEvent))
			{
				var parameters = new TestProtocolCreationParameters(
					(SportEnum)SelectedSport,
					(LevelEnum)SelectedLevel,
					SelectedEvent,
					eventId.Value);

				await _navigationService.NavigateToPage<TestProtocolFinalCreationPage>(parameters);
			}
		}

		protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
		{
			base.ViewModelBase_PropertyChanged(sender, e);

			if (e.PropertyName == nameof(SelectedSport) ||
				e.PropertyName == nameof(SelectedLevel) ||
				e.PropertyName == nameof(SelectedEvent))
			{
				IsNextButtonEnabled = SelectedSport != null &&
					SelectedLevel != null &&
					!string.IsNullOrEmpty(SelectedEvent);
			}

			if (e.PropertyName == nameof(SelectedSport) &&
				e.PropertyName != nameof(SelectedLevel) &&
				e.PropertyName != nameof(SelectedEvent))
			{
				IsNextButtonEnabled = SelectedSport != null;
			}

			if (e.PropertyName == nameof(SelectedSport))
			{
				EventList = trainingPlanEvents
					.Where(x => x.Sport == (int)SelectedSport)?
					.Select(x => x.EventName)
					.ToObservableCollection();
			}
		}

		public override async Task OnAppearing()
		{
			IsBusy = true;

			trainingPlanEvents = await ServiceHelper
				.GetService<TrainingPlanEventService>()
				.GetTrainingPlanEvents();

			IsBusy = false;
        }
	} 
}

