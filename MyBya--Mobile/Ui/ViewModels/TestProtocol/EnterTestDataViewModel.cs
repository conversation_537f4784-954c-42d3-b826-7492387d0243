using System.Windows.Input;
using MyBya.Constants;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.BepsTestData;
using MyBya.Models.TestCalendar;
using MyBya.Models.TestDataEntry;
using MyBya.Services.BepsTestData;
using MyBya.Services.DTOs.TestDataEntry;
using MyBya.Services.TestCalendar;
using MyBya.Services.TestDataEntry;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class EnterTestDataViewModel : ListViewModelBase<TestCalendarDetailsModel>
{
    private AthleteTestSetupModel? CurrentTestSetup {get; set;}

    private List<TestCalendarDetailsModel> _intervals {get; set;}

    private string standardInterval;
    public string StandardInterval 
    {
        get => standardInterval;
        set => SetProperty(ref standardInterval, value);
    }

    private string intervalTypeTitle;
    public string IntervalTypeTitle 
    {
        get => intervalTypeTitle;
        set => SetProperty(ref intervalTypeTitle, value);
    }

    private bool isSubmitButtonEnabled;
    public bool IsSubmitButtonEnabled
    {
        get => isSubmitButtonEnabled;
        set => SetProperty(ref isSubmitButtonEnabled, value);
    }

    private bool isAddNextIntervalVisible = true;
    public bool IsAddNextIntervalVisible
    {
        get => isAddNextIntervalVisible;
        set => SetProperty(ref isAddNextIntervalVisible, value);
    }

    private int intervalsCount;
    
    public ICommand SubmitCommand {get; set;}
    public ICommand AddNextIntervalCommand {get; set;}

    private bool _isSubmitButtonVisible;
    public bool IsSubmitButtonVisible
    {
        get => _isSubmitButtonVisible;
        set => SetProperty(ref _isSubmitButtonVisible, value);
    }

    private bool _isSecondarySubmitButtonVisible;
    public bool IsSecondarySubmitButtonVisible
    {
        get => _isSecondarySubmitButtonVisible;
        set => SetProperty(ref _isSecondarySubmitButtonVisible, value);
    }

    private bool _isEightIntervalsVisible;
    public bool IsEightIntervalsVisible
    {
        get => _isEightIntervalsVisible;
        set => SetProperty(ref _isEightIntervalsVisible, value);
    }

    private bool _isFourIntervalsVisible;
    public bool IsFourIntervalsVisible
    {
        get => _isFourIntervalsVisible;
        set => SetProperty(ref _isFourIntervalsVisible, value);
    }

    private bool isBlacVisible;
    public bool IsBlacVisible
    {
        get => isBlacVisible;
        set => SetProperty(ref isBlacVisible, value);
    }

    public ICommand NavigateBackCommand =>
        new SyncedCommand(_navigationService.NavigateBack);

    public EnterTestDataViewModel()
    {
        SubmitCommand = new SyncedCommand(Submit);
        AddNextIntervalCommand = new SyncedCommand(AddNextInterval, showLoading: true);
        IsSubmitButtonVisible = true;
        IsSecondarySubmitButtonVisible = false;
    }

    public async Task Submit()
    {
        try
        {
            OpenLoadingView = true;

            await PostLastInterval();

            BepsTestDataModel? bepsTestDataModel = await ServiceHelper
                .GetService<BepsTestDataService>()
                .CreateBepsResult(CurrentTestSetup.TestDetailAthleteId);

            if (bepsTestDataModel is null) 
            {
                await ShowMessageError();
                return;
            }

            OpenLoadingView = false;
            await _navigationService.NavigateToPage<BepsScoresPage>(bepsTestDataModel);
        }
        catch (Exception ex)
        {   
            OpenLoadingView = false;
            throw ex;
        }
    }

    private async Task PostLastInterval()
    {
        TestCalendarDetailsModel? currentInterval = Items.LastOrDefault();

        if ((IsDistanceBasedInterval(currentInterval) && string.IsNullOrEmpty(currentInterval.Time))
            || (IsTimeBasedInterval(currentInterval) && currentInterval.ActualPaceMph == 0)
            || currentInterval.HeartRate == 0
            || (IsBlacVisible && currentInterval.BLacValue == 0))
        {
            string message = "Please fill in all required fields.";
            await ShowToastMessage(message);
            return;
        }
        
        if (currentInterval.HeartRate > 250)
        {
            string message = "The Heart Rate value of 250 bpm is invalid. Please enter a valid heart rate within the acceptable range.";
            await ShowToastMessage(message);
            return;
        }

        if (Items.Count > 1)
        {
            bool flowControl = await ValidateCurrentInterval(currentInterval);

            if (!flowControl)
                return;
        }

        await PostCurrentTestDataEntry(currentInterval);
        SetNextInterval();
    }

    private async Task AddNextInterval()
    {
        await PostLastInterval();
    }

    private async Task<bool> ValidateCurrentInterval(TestCalendarDetailsModel item)
    {
        int currentInterval = item.IntervalCount;

        TestCalendarDetailsModel? previousItem = Items
            .FirstOrDefault(x => x.IntervalCount == currentInterval - 1);

        if (IsDistanceBasedInterval(item))
        {
            if (IsUserIncreasingTime(previousItem, currentItem: item))
            {
                string message = $"Time must be less than or equal to the Previous stage";
                await ShowToastMessage(message);
                return false;
            }
        }
        else if (IsTimeBasedInterval(item))
        {
            if (IsUserIncreasingPaceMph(previousItem, currentItem: item))
            {
                string message = "Speed must be less than or equal to the Previous stage";
                await ShowToastMessage(message);
                return false;
            }
        }
        else
        {
            //Watts
        }

        if (IsUserDecreasingHeartRate(previousItem, currentItem: item))
        {
            string message = $"Heart Rate must be greater than or equal to the Previous stage";
            await ShowToastMessage(message);
            return false;
        }

        if (IsUserDecreasingBLac(previousItem, currentItem: item))
        {
            string message = $"BLac must be greater than or equal to the Previous stage";
            await ShowToastMessage(message);
            return false;
        }

        return true;
    }

    private bool IsDistanceBasedInterval(TestCalendarDetailsModel item)
    {
        return item.IntervalType.Value == IntervalTypeEnum.DistanceBased;
    }

    private bool IsTimeBasedInterval(TestCalendarDetailsModel item)
    {
        return item.IntervalType.Value == IntervalTypeEnum.TimeBased;
    }

    private bool IsUserIncreasingPaceMph(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.ActualPaceMph > previousItem.ActualPaceMph;
    }

    private bool IsUserDecreasingBLac(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.BLacValue < previousItem.BLacValue;
    }
    
    private bool IsUserDecreasingHeartRate(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.HeartRate < previousItem.HeartRate;
    }

    private bool IsUserIncreasingTime(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        TimeSpan previousTimeSpan = TimeSpan.Parse(previousItem.Time);
        TimeSpan currentTimeSpan = TimeSpan.Parse(currentItem.Time);
        return currentTimeSpan > previousTimeSpan;
    }

    private void SetNextInterval()
    {
        intervalsCount++;

        TestCalendarDetailsModel? testCalendarDTO = _intervals
            .FirstOrDefault(x => x.IntervalCount == intervalsCount);

        Items.Add(testCalendarDTO);

        if (IsFourIntervalsVisible && intervalsCount == 4)
        {
            IsAddNextIntervalVisible = false;
        }
        else if (intervalsCount == 5)
        {
            IsSubmitButtonVisible = false;
            IsSecondarySubmitButtonVisible = true;
        }

        if (intervalsCount == TestProtocolConstants.MINIMUM_INTERVAL_COUNT)
        {
            IsSubmitButtonEnabled = true;
        }
        else if (_intervals.Max(x => x.IntervalCount) == intervalsCount)
        {
            IsAddNextIntervalVisible = false;
        }
    }

    private async Task PostCurrentTestDataEntry(TestCalendarDetailsModel? item)
    {
        int? minutes;
        int? seconds;
        
        if (item.IntervalType.Value == IntervalTypeEnum.TimeBased)
        {
            (minutes, seconds) = CalculateTreadmillTime(item);
        }
        else
        {
            TimeSpan time = TimeSpan.Parse($"00:{item.Time}");
            minutes = time.Minutes;
            seconds = time.Seconds;
        }
        
        var testDataEntryDto = new TestDataEntryDto
        {
            TestDetailAthleteId = CurrentTestSetup.TestDetailAthleteId,
            Interval = item.IntervalCount,
            IntervalDistance = item.StandardInterval,
            IntervalTime = minutes,
            IntervalSecond = seconds,
            HeartRate = item.HeartRate,
            Blac = item.BLacValue
        };

        if (!IsBlacVisible)
        {
            switch (item.IntervalCount)
            {
                case 1:
                    testDataEntryDto.Blac = 3;
                    break;
                case 2:
                    testDataEntryDto.Blac = 5;
                    break;
                case 3:
                    testDataEntryDto.Blac = 7;
                    break;
                case 4:
                    testDataEntryDto.Blac = 9;
                    break;
            }
        }
    
        TestDataEntryModel? testDataEntryModel = await ServiceHelper
            .GetService<TestDataEntryService>()
            .SendTestDataEntry(testDataEntryDto);
        
        if (testDataEntryModel is null) 
        {
            string message = $"An error has occured while posting the current interval: {item.IntervalCount}. Please try again later.";
            await ShowMessageError(message);
        }
    }

    private (int minutes, int seconds) CalculateTreadmillTime(TestCalendarDetailsModel? item) 
    {
        double speedResult = item.ActualPaceMph * 1609.34 / 3600;
        double timeResult = item.StandardInterval.Value / speedResult;
        TimeSpan ts = TimeSpan.FromSeconds(timeResult);
        return (ts.Minutes, ts.Seconds);
    }

    public override async Task OnAppearing()
    {
        CurrentTestSetup = MyByaContext.Instance.CurrentTestSetup;
        TestCalendarModel? testCalendar = MyByaContext.Instance.CurrentTestCalendar;

        IsEightIntervalsVisible = true;

        if (testCalendar is null)
        {
            await ShowMessageError();
            return;
        }

        SetHeartRateOnlyTest();
        SetHeartRateAndBlacTest();
        SetStandardInterval(testCalendar);
        SetIntervalTypeTitle(testCalendar);
        SetIntervals(testCalendar);
    }

    private void SetHeartRateAndBlacTest()
    {
        IsEightIntervalsVisible = CurrentTestSetup.DataType == 1;
        IsBlacVisible = IsEightIntervalsVisible;
    }

    private void SetHeartRateOnlyTest()
    {
        IsFourIntervalsVisible = CurrentTestSetup.DataType == 0;
    }
    
    private void SetIntervals(TestCalendarModel testCalendar)
    {
        for (int i = 0; i < TestProtocolConstants.NUMBER_OF_INTERVALS; i++)
        {
            var model = new TestCalendarDetailsModel
            {
                IntervalCount = i + 1,
                IntervalType = testCalendar.IntervalType.Value,
                StandardInterval = testCalendar.StandardInterval,
                IsBlacVisible = IsBlacVisible
            };

            model.SetActualPaceInputVisibility(model.IntervalType.Value);

            if (model.IntervalCount == 1)
            {
                intervalsCount++;
                Items.Add(model);
                continue;
            }

            _intervals ??= new List<TestCalendarDetailsModel>();
            _intervals.Add(model);
        }
    }

    private void SetIntervalTypeTitle(TestCalendarModel testCalendar)
    {
        IntervalTypeTitle = testCalendar.IntervalType == IntervalTypeEnum.DistanceBased
            ? "Actual Time"
            : "Actual Pace";
    }

    private void SetStandardInterval(TestCalendarModel testCalendar)
    {
        StandardInterval = $"{testCalendar.StandardInterval.Value}m Stages";
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        CurrentTestSetup = (AthleteTestSetupModel?)parameter;
        return base.OnNavigatingTo(parameter);
    }
}