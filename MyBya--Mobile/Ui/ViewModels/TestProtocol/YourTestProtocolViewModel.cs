using System.Windows.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models.TestProtocolInstruction;
using MyBya.Services.TestProtocolInstruction;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol
{
    public class YourTestProtocolViewModel : ViewModelBase
    {
        private TestProtocolParameters _testProtocolParameters;

        private string _sportAndEvent;
        public string SportAndEvent
        {
            get => _sportAndEvent;
            set => SetProperty(ref _sportAndEvent, value);
        }

        private string _level;
        public string Level
        {
            get => _level;
            set => SetProperty(ref _level, value);
        }

        private string _raceGoal;
        public string RaceGoal
        {
            get => _raceGoal;
            set => SetProperty(ref _raceGoal, value);
        }

        private string _testLocation;
        public string TestLocation
        {
            get => _testLocation;
            set => SetProperty(ref _testLocation, value);
        }

        private string _dataCapture;
        public string DataCapture
        {
            get => _dataCapture;
            set => SetProperty(ref _dataCapture, value);
        }

        private TestProtocolInstructionModel testProtocolInstruction;
        public TestProtocolInstructionModel TestProtocolInstruction
        {
            get => testProtocolInstruction;
            set => SetProperty(ref testProtocolInstruction, value);
        }

        public ICommand NavigateToEnterTestDataCommand { get; private set; }
        public ICommand EditTestingInputCommand { get; set; }

        public YourTestProtocolViewModel()
        {
            NavigateToEnterTestDataCommand = new SyncedCommand(Navigate);
            EditTestingInputCommand = new SyncedCommand(_navigationService.NavigateBack);
        }

        private async Task Navigate()
        {
            await _navigationService.NavigateToPage<EnterTestDataPage>();
        }

        public override Task OnNavigatingTo(object? parameter)
        {
            if (parameter is TestProtocolParameters testProtocolParameters)
            {
                _testProtocolParameters = testProtocolParameters;    

                SportAndEvent = $"{_testProtocolParameters.Sport}, {_testProtocolParameters.Event}";
                RaceGoal = _testProtocolParameters.Time;
                TestLocation = _testProtocolParameters.IsTreadmill
                    ? RunningModeEnum.TREADMILL.ToString()
                    : RunningModeEnum.TRACK.ToString();
                DataCapture = _testProtocolParameters.IsHeartRateAndLactate
                    ? "Heart Rate and Lactate levels"
                    : "Heart Rate only";
            }

            return base.OnNavigatingTo(parameter);
        }

        public override async Task OnAppearing()
        {
            int sport = (int)_testProtocolParameters.Sport;
            int interval_type = (int)_testProtocolParameters.IntervalType;
            int interval_distance = _testProtocolParameters.IntervalDistance;

            List<TestProtocolInstructionModel> instrictons = await ServiceHelper
                .GetService<TestProtocolInstructionService>()
                .GetTestProtocolInstructions(sport, interval_type, interval_distance);

            if (instrictons != null && instrictons.Count > 0)
                TestProtocolInstruction = instrictons[0];
        }
    }
}