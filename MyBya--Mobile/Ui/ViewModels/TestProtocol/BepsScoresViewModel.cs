using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Models.BepsTestData;
using MyBya.Models.TestResult;
using MyBya.Services.TestResult;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class BepsScoresViewModel : ListViewModelBase<BeepScoresModel>
{
    private BepsTestDataModel CurrentBepsTestData {get; set;}

    public ICommand ContinueCommand { get; set; }

    public BepsScoresViewModel()
    {
        ContinueCommand = new SyncedCommand(Continue, showLoading: true);
    }

    private async Task Continue()
    {
        await _navigationService.NavigateToPage<TrainingTargetsPage>();
    }

    public override async Task OnAppearing()
    {
        if (CurrentBepsTestData is null)
        {
            await ShowMessageError();
            return;
        }

        Items = new ObservableCollection<BeepScoresModel>() 
        {
            new (BepsScoresEnum.AF, CurrentBepsTestData.Af),
            new (BepsScoresEnum.PAC, CurrentBepsTestData.Pac),
            new (BepsScoresEnum.LTCCC, CurrentBepsTestData.Ltcc),
            new (BepsScoresEnum.ARC3, CurrentBepsTestData.Arc3),
            new (BepsScoresEnum.ARC2, CurrentBepsTestData.Arc2),
            new (BepsScoresEnum.ARC1, CurrentBepsTestData.Arc1),
            new (BepsScoresEnum.ANRC2, CurrentBepsTestData.Anrc2),
            new (BepsScoresEnum.ANRC1, CurrentBepsTestData.Anrc1)
        };
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is null)
            return Task.CompletedTask;

        CurrentBepsTestData = (BepsTestDataModel)parameter;
        return base.OnNavigatingTo(parameter);
    }
}
