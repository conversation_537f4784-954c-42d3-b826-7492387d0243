using System.ComponentModel;
using System.Windows.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.TestCalendar;
using MyBya.Parameters;
using MyBya.Services.AthleteTestSetup;
using MyBya.Services.DTOs.AthleteTestSetup;
using MyBya.Services.TestCalendar;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class TestProtocolFinalCreationViewModel : ViewModelBase
{
    private TestProtocolCreationParameters testProtocolCreationParameters { get; set; }

    private SportEnum _selectedSport;
    private LevelEnum _selectedLevel;
    private string _selectedEvent;
    private int eventId;

    public SportEnum SelectedSport
    {
        get => _selectedSport;
        set => SetProperty(ref _selectedSport, value);
    }

    public LevelEnum SelectedLevel
    {
        get => _selectedLevel;
        set => SetProperty(ref _selectedLevel, value);
    }

    public string SelectedEvent
    {
        get => _selectedEvent;
        set => SetProperty(ref _selectedEvent, value);
    }

    private bool isFinishButtonEnabled;
    public bool IsFinishButtonEnabled
    {
        get => isFinishButtonEnabled;
        set => SetProperty(ref isFinishButtonEnabled, value);
    }

    private int minutes;
    public int Minutes
    {
        get => minutes;
        set => SetProperty(ref minutes, value);
    }

    private int seconds;
    public int Seconds
    {
        get => seconds;
        set => SetProperty(ref seconds, value);
    }

    private bool isTreadmillSelected;
    public bool IsTreadmillSelected
    {
        get => isTreadmillSelected;
        set => SetProperty(ref isTreadmillSelected, value);
    }

    private bool isTrackSelected;
    public bool IsTrackSelected
    {
        get => isTrackSelected;
        set => SetProperty(ref isTrackSelected, value);
    }

    private bool isHeartRateAndLactateSelected;
    public bool IsHeartRateAndLactateSelected
    {
        get => isHeartRateAndLactateSelected;
        set => SetProperty(ref isHeartRateAndLactateSelected, value);
    }

    private bool isHeartRateOnlySelected;
    public bool IsHeartRateOnlySelected
    {
        get => isHeartRateOnlySelected;
        set => SetProperty(ref isHeartRateOnlySelected, value);
    }

    public ICommand FinishButtonCommand { get; set; }
    public ICommand BackButtonCommand { get; set; }

    public TestProtocolFinalCreationViewModel()
    {
        IsFinishButtonEnabled = false;
        FinishButtonCommand = new SyncedCommand(Finish, showLoading: true);
        BackButtonCommand = new SyncedCommand(Back);
    }

    private async Task Back()
    {
        await _navigationService.NavigateBack();
    }

    private async Task Finish()
    {
        var formattedTime = $"{Minutes:D2}:{Seconds:D2}";
        
        var entity = new AthleteTestSetupDto
        {
            DataType = IsHeartRateOnlySelected ? 0 : 1,
            UserId = 942,
            Sport = SelectedSport,
            Time = formattedTime,
            IntervalType = IsTreadmillSelected
                ? IntervalTypeEnum.TimeBased
                : IntervalTypeEnum.DistanceBased,
            Level = SelectedLevel,
            EventId = eventId,
        };

        AthleteTestSetupModel? model = await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .CreateAthleteTest(entity);
            
        if (model != null)
        {
            MyByaContext.Instance.SetCurrentTestSetup(model);

            TestCalendarModel? testCalendar = await ServiceHelper
                .GetService<TestCalendarService>()
                .GetTestCalendar(model.TestCalendarId.Value);

            if (testCalendar == null)
            {
                return;
            }

            MyByaContext.Instance.SetCurrentTestCalendar(testCalendar);

            var parameters = new TestProtocolParameters
            {
                Sport = SelectedSport,
                IntervalType = entity.IntervalType,
                IntervalDistance = testCalendar.StandardInterval.Value,
                Level = SelectedLevel,
                Event = SelectedEvent,
                Time = formattedTime,
                IsTreadmill = IsTreadmillSelected,
                IsTrack = IsTrackSelected,
                IsHeartRateAndLactate = IsHeartRateAndLactateSelected,
                IsHeartRateOnly = IsHeartRateOnlySelected
            };

            await _navigationService.NavigateToPage<YourTestProtocolPage>(parameters);
        }
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is TestProtocolCreationParameters parameters)
        {
            testProtocolCreationParameters = parameters;
            SelectedSport = parameters.Sport;
            SelectedLevel = parameters.Level;
            SelectedEvent = parameters.Event;
            eventId = parameters.EventId;
        }

        return base.OnNavigatingTo(parameter);
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        base.ViewModelBase_PropertyChanged(sender, e);

        if (e.PropertyName == nameof(IsTreadmillSelected) ||
            e.PropertyName == nameof(IsTrackSelected) ||
            e.PropertyName == nameof(IsHeartRateAndLactateSelected) ||
            e.PropertyName == nameof(IsHeartRateOnlySelected) ||
            e.PropertyName == nameof(Minutes) || e.PropertyName == nameof(Seconds))
        {
            IsFinishButtonEnabled = (Minutes > 0 || Seconds > 0) &&
                                    (IsTreadmillSelected || IsTrackSelected) &&
                                    (IsHeartRateAndLactateSelected || IsHeartRateOnlySelected);
        }
    }
}
