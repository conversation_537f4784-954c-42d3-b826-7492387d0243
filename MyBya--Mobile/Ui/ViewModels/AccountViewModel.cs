using System.Windows.Input;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class AccountViewModel : ViewModelBase
{
    private string _userName = "User";
    public string UserName
    {
        get => _userName;
        set => SetProperty(ref _userName, value);
    }

    private string _userEmail = "<EMAIL>";
    public string UserEmail
    {
        get => _userEmail;
        set => SetProperty(ref _userEmail, value);
    }

    private string _appVersion = "1.0.0";
    public string AppVersion
    {
        get => _appVersion;
        set => SetProperty(ref _appVersion, value);
    }

    public ICommand EditProfileCommand { get; }
    public ICommand NotificationSettingsCommand { get; }
    public ICommand PrivacyPolicyCommand { get; }
    public ICommand TermsOfServiceCommand { get; }
    public ICommand AboutCommand { get; }
    public ICommand LogoutCommand { get; }

    public AccountViewModel()
    {
        EditProfileCommand = new Command(OnEditProfile);
        NotificationSettingsCommand = new Command(OnNotificationSettings);
        PrivacyPolicyCommand = new Command(OnPrivacyPolicy);
        TermsOfServiceCommand = new Command(OnTermsOfService);
        AboutCommand = new Command(OnAbout);
        LogoutCommand = new Command(OnLogout);
        
        LoadUserInfo();
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        LoadUserInfo();
    }

    private void LoadUserInfo()
    {
        // For now, we'll use placeholder data
        // In the future, this would load actual user data from a service
        UserName = "John Doe";
        UserEmail = "<EMAIL>";
        AppVersion = AppInfo.VersionString;
    }

    private async void OnEditProfile()
    {
        await ShowToastMessage("Edit Profile feature coming soon!");
    }

    private async void OnNotificationSettings()
    {
        await ShowToastMessage("Notification Settings feature coming soon!");
    }

    private async void OnPrivacyPolicy()
    {
        await ShowToastMessage("Privacy Policy feature coming soon!");
    }

    private async void OnTermsOfService()
    {
        await ShowToastMessage("Terms of Service feature coming soon!");
    }

    private async void OnAbout()
    {
        await _alertService.ShowAlertAsync("About MyBya", 
            $"MyBya Mobile App\nVersion: {AppVersion}\n\nYour fitness companion for test protocols and training.");
    }

    private async void OnLogout()
    {
        bool result = await _alertService.ShowConfirmationAsync("Logout", 
            "Are you sure you want to logout?", "Yes", "No");
        
        if (result)
        {
            await ShowToastMessage("Logout feature coming soon!");
        }
    }
}
