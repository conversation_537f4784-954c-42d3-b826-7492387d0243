using System.Collections.ObjectModel;
using System.Text;
using MyBya.Constants;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models.TestResultPaceChart;
using MyBya.Models.TrainingPlanDetail;
using MyBya.Parameters;
using MyBya.Ui.ViewModels.Common;
using Newtonsoft.Json.Linq;
namespace MyBya.Ui.ViewModels;
public partial class TrainingScheduleResultViewModel : ViewModelBase
{ 
    private List<TrainingPlanDetailModel> _workouts;
    private ObservableCollection<WorkoutLabels> workoutLabels;
    public ObservableCollection<WorkoutLabels> WorkoutLabels
    {
        get => workoutLabels;
        set => SetProperty(ref workoutLabels, value);
    }
    private string titleText;
    public string TitleText
    {
        get => titleText;
        set => SetProperty(ref titleText, value);
    }
    private string comment;
    public string Comment
    {
        get => comment;
        set => SetProperty(ref comment, value);
    }
    public TrainingScheduleResultViewModel()
    {
        WorkoutLabels = new ObservableCollection<WorkoutLabels>();
    }
    public override Task OnNavigatingTo(object? parameter)
    {
        var trainingPlanDetail = (TrainingPlanDetailParameters?)parameter;

        if (trainingPlanDetail is null)
            return Task.CompletedTask;
            
        TitleText = trainingPlanDetail.SelectedDate;
        _workouts = trainingPlanDetail.Workouts;

        return base.OnNavigatingTo(parameter);
    }
    private async Task ProcessWorkout(string workoutName)
    {
        var dictionary = BuildWorkoutDictionary(workoutName);
        var currentWorkout = _workouts.FirstOrDefault(x => x.WorkoutName == workoutName);
        
        var finalString = new StringBuilder();
        var configArray = JArray.Parse(currentWorkout?.Configurable ?? "");
        var fieldsArray = (JArray)((JArray)configArray[0])[1];

        finalString.AppendLine(workoutName);
        finalString.AppendLine(currentWorkout?.WorkoutDescription ?? "");

        await ProcessWorkoutFields(fieldsArray, dictionary, finalString);
        WorkoutLabels.Add(new WorkoutLabels { WorkoutLabel = finalString.ToString() });
    }

    private Dictionary<string, string> BuildWorkoutDictionary(string workoutName)
    {
        return _workouts
            .Where(w => w.WorkoutName == workoutName)
            .ToDictionary(w => w.FieldName, w => w.FieldValue);
    }

    private async Task ProcessWorkoutFields(JArray fieldsArray, Dictionary<string, string> dictionary, StringBuilder finalString)
    {
        foreach (var entry in fieldsArray)
        {
            var fieldName = entry[0]?.ToString();
            var cfg = entry[1];
            if (!IsValidField(fieldName, dictionary))
                continue;

            string labelText = "";

            if (fieldName == "target_heartrate")
                await ProcessHeartRate(dictionary[fieldName], finalString);
            else if (fieldName == "pace")
                await ProcessPace(dictionary, finalString);
            else if (fieldName == "session_volume")
                ProcessSessionVolume(dictionary[fieldName], finalString);
            else if (cfg.Type == JTokenType.Object && cfg["label"] != null)
                ProcessGenericField(cfg, dictionary[fieldName], finalString);
        }
    }
    private bool IsValidField(string fieldName, Dictionary<string, string> dictionary)
    {
        return !string.IsNullOrWhiteSpace(fieldName)
            && dictionary.ContainsKey(fieldName)
            && !string.IsNullOrEmpty(dictionary[fieldName]);
    }
    private async Task ProcessHeartRate(string heartRateValue, StringBuilder finalString)
    {
        var labelText = $"{heartRateValue}: ";

        var allItems = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectAllItems();

        var heartRateItem = allItems.FirstOrDefault();

        if (heartRateItem != null && heartRateValue.Contains("-"))
        {
            foreach (var zone in heartRateValue.Split('-'))
            {
                labelText += GetHeartRateZoneText(zone.Trim(), heartRateItem);
            }
            finalString.AppendLine(labelText);
        }
    }

    private string GetHeartRateZoneText(string zone, TestResultHeartRatesEntity heartRateItem) => zone switch
    {
        HeartRateZonesConstants.ZONE_1 => $"< {heartRateItem.Zone1} - ",
        HeartRateZonesConstants.ZONE_2_LOW => $"[{heartRateItem.Zone2Low}]",
        HeartRateZonesConstants.ZONE_2_MID => $"[{heartRateItem.Zone2Mid}]",
        HeartRateZonesConstants.ZONE_2_HIGH => $"[{heartRateItem.Zone2High}]",
        HeartRateZonesConstants.ZONE_3 => $"[{heartRateItem.Zone3}]",
        _ => string.Empty
    };

    private async Task ProcessPace(Dictionary<string, string> dictionary, StringBuilder finalString)
    {
        string intervalDistance = dictionary["interval_distance"];
        string paceFieldName = dictionary["pace"];

        var paces = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .GetPacesByInterval(intervalDistance);

        var minPace = paces.FirstOrDefault(x => x.System.Contains("Min"));
        var maxPace = paces.FirstOrDefault(x => x.System.Contains("Max"));

        string paceText = GetPaceText(paceFieldName, intervalDistance, minPace, maxPace);

        if (!string.IsNullOrEmpty(paceText))
            finalString.AppendLine(paceText);
    }

    private string GetPaceText(string paceType, string distance, TestResultPaceChartModel minPace, TestResultPaceChartModel maxPace)
    {
        return paceType switch
        {
            PacesConstants.ARC1 => $"{distance} @ {paceType}: {minPace.Arc1} - {maxPace.Arc1}",
            PacesConstants.ARC2 => $"{distance} @ {paceType}: {minPace.Arc2} - {maxPace.Arc2}",
            PacesConstants.ARC3 => $"{distance} @ {paceType}: {minPace.Arc3} - {maxPace.Arc3}",
            PacesConstants.LTCC => $"{distance} @ {paceType}: {minPace.Ltcc} - {maxPace.Ltcc}",
            _ => string.Empty
        };
    }

    private void ProcessSessionVolume(string sessionVolumeValue, StringBuilder finalString)
    {
        finalString.AppendLine(sessionVolumeValue);
    }

    private void ProcessGenericField(JToken cfg, string fieldValue, StringBuilder finalString)
    {
        string label = cfg.Value<string>("label");
        finalString.AppendLine($"{label}: {fieldValue}");
    }

    public override async Task OnAppearing()
    {
        List<string> workoutNames = _workouts?
            .Select(x => x.WorkoutName)
            .Distinct()
            .ToList();

        if (workoutNames.FirstOrDefault() == "Day Off")
        {
            WorkoutLabels.Add(new WorkoutLabels { WorkoutLabel = "Day Off" });
            return;
        }

        if (!string.IsNullOrEmpty(_workouts.FirstOrDefault()?.Comment))
        {
            Comment = _workouts.FirstOrDefault()?.Comment;
        }

        foreach (string workoutName in workoutNames)
        {
            await ProcessWorkout(workoutName);
        }
    }
}
public class WorkoutLabels
{
    public string WorkoutLabel { get; set; }
}