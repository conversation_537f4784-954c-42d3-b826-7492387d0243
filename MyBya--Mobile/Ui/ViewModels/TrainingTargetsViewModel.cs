using System.Collections.ObjectModel;
using System.Windows.Input;
using AutoMapper;
using MyBya.DataAccess;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models.TestResultHeartRate;
using MyBya.Models.TestResultPaceChart;
using MyBya.Services.TestResultAnrc;
using MyBya.Services.TestResultHeartRate;
using MyBya.Services.TestResultPaceChart;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class TrainingTargetsViewModel : ViewModelBase
{
    private TestResultHeartRateModel? heartRates;
    public TestResultHeartRateModel? HeartRates
    {
        get => heartRates;
        set => SetProperty(ref heartRates, value);
    }

    private ObservableCollection<TestResultPaceChartModel> paceCharts;
    public ObservableCollection<TestResultPaceChartModel> PaceCharts
    {
        get => paceCharts;
        set => SetProperty(ref paceCharts, value);
    }

    public ICommand ContinueCommand { get; set; }

    public TrainingTargetsViewModel()
    {
        ContinueCommand = new SyncedCommand(Continue, showLoading: true);
    }

    private async Task Continue()
    {
        await _navigationService.NavigateToPage<TrainingSchedulePage>();
    }

    public override async Task OnAppearing()
    {
        try
        {
            IsBusy = true;

            await SetHeartRates();
            await SetPaceCharts();
            await SetAnrcs();

            IsBusy = false;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    private async Task SetAnrcs()
    {
        var anrcsItems = await ServiceHelper
            .GetService<TestResultAnrcService>()
            .GetTestResultsAnrc();

        // it doesnt work for PPT protocol. Waiting for the new ones to update the table.
    }

    private async Task SetPaceCharts()
    {
        List<TestResultPaceChartModel> paceCharts = await ServiceHelper
           .GetService<TestResultPaceChartService>()
           .GetPaceCharts();

        if (paceCharts is null)
        {
            await ShowMessageError();
            return;
        }

        await Task.Run(async () => await SetPaceChartsLocally(paceCharts));

        var paceChartsDtoList = new List<TestResultPaceChartModel>();
        TestResultPaceChartModel? newPaceChart = null;

        foreach (TestResultPaceChartModel item in paceCharts)
        {
            string currentSystem = item.System.Split('-')[0];

            if (item.System.Contains("Max"))
            {
                newPaceChart = new TestResultPaceChartModel();
                newPaceChart.System = item.System.Split('-')[0];
                newPaceChart.Arc1Max = item.Arc1;
                newPaceChart.Arc2Max = item.Arc2;
                newPaceChart.Arc3Max = item.Arc3;
                newPaceChart.LtccMax = item.Ltcc;
                continue;
            }

            if (item.System.Contains("Min"))
            {
                newPaceChart.Arc1Min = item.Arc1;
                newPaceChart.Arc2Min = item.Arc2;
                newPaceChart.Arc3Min = item.Arc3;
                newPaceChart.LtccMin = item.Ltcc;
            }

            paceChartsDtoList.Add(newPaceChart);
        }

        var orderedList = paceChartsDtoList
            .OrderBy(x => int.Parse(x.System.Split('m')[0]))
            .ToList();

        PaceCharts = new ObservableCollection<TestResultPaceChartModel>(orderedList);
    }

    public async Task SetHeartRates()
    {
        TestResultHeartRateModel? heartRates = await ServiceHelper
            .GetService<TestResultHeartRateService>()
            .GetTestResultHeartRate();

        if (heartRates is null)
        {
            await ShowMessageError();
            return;
        }

        await Task.Run(async () => await SetHeartRatesLocally(heartRates));
        HeartRates = heartRates;
    }

    private async Task SetHeartRatesLocally(TestResultHeartRateModel? heartRates)
    {
        int lowFirst = heartRates.Zone2Min.Value;
        int lowLast = lowFirst + 6;
        int midFirst = lowLast + 1;
        int midLast = midFirst + 6;
        int highFirst = midLast + 1;
        int highLast = highFirst + 6;

        heartRates.Zone1 = $"{heartRates.Zone1Max.Value}";
        heartRates.Zone2Low = $"{lowFirst} - {lowLast}";
        heartRates.Zone2Mid = $"{midFirst} - {midLast}";
        heartRates.Zone2High = $"{highFirst} - {highLast}";
        heartRates.Zone3 = $"{heartRates.Zone3Min.Value} - {heartRates.Zone3Max.Value}";

        var entity = Mapper.Map<TestResultHeartRatesEntity>(heartRates);

        var testResultHeartRateDataAccess = ServiceHelper
            .GetService<ITestResultHeartRateRepository>();

        await testResultHeartRateDataAccess.Clear();
        await testResultHeartRateDataAccess.Insert(entity);
    }

    private async Task SetPaceChartsLocally(List<TestResultPaceChartModel> paceCharts)
    {
        var testResultPaceChartDataAccess = ServiceHelper.GetService<ITestResultPaceChartRepository>();
        await testResultPaceChartDataAccess.Clear();

        var entites = new List<TestResultPaceChartEntity>();

        foreach (var model in paceCharts)
        {
            var entity = Mapper.Map<TestResultPaceChartEntity>(model);
            entites.Add(entity);
        }

        await testResultPaceChartDataAccess.InsertAll(entites);
    }
}