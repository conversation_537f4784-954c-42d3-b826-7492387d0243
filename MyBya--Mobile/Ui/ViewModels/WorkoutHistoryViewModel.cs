using System.Collections.ObjectModel;
using System.Windows.Input;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class WorkoutHistoryViewModel : ViewModelBase
{
    private ObservableCollection<WorkoutHistoryItem> _workoutHistory = new();
    public ObservableCollection<WorkoutHistoryItem> WorkoutHistory
    {
        get => _workoutHistory;
        set => SetProperty(ref _workoutHistory, value);
    }

    private bool _hasWorkouts;
    public bool HasWorkouts
    {
        get => _hasWorkouts;
        set => SetProperty(ref _hasWorkouts, value);
    }

    public ICommand NavigateToTestProtocolCommand { get; }

    public WorkoutHistoryViewModel()
    {
        NavigateToTestProtocolCommand = new Command(OnNavigateToTestProtocol);
        LoadWorkoutHistory();
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        LoadWorkoutHistory();
    }

    private void LoadWorkoutHistory()
    {
        // For now, we'll show a placeholder message
        // In the future, this would load actual workout data from a service
        WorkoutHistory.Clear();
        
        // Add some sample data for demonstration
        // Remove this when implementing real data loading
        WorkoutHistory.Add(new WorkoutHistoryItem
        {
            Date = DateTime.Now.AddDays(-1),
            WorkoutType = "Test Protocol",
            Duration = "45 minutes",
            Notes = "Completed running assessment"
        });
        
        WorkoutHistory.Add(new WorkoutHistoryItem
        {
            Date = DateTime.Now.AddDays(-7),
            WorkoutType = "Training Session",
            Duration = "60 minutes",
            Notes = "Interval training"
        });

        HasWorkouts = WorkoutHistory.Count > 0;
    }

    private async void OnNavigateToTestProtocol()
    {
        await Shell.Current.GoToAsync("//TestProtocolPage");
    }
}

public class WorkoutHistoryItem
{
    public DateTime Date { get; set; }
    public string WorkoutType { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public string FormattedDate => Date.ToString("MMM dd, yyyy");
}
