using System.Windows.Input;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class HomeViewModel : ViewModelBase
{
    private string _welcomeMessage = "Welcome to MyBya";
    public string WelcomeMessage
    {
        get => _welcomeMessage;
        set => SetProperty(ref _welcomeMessage, value);
    }

    public ICommand NavigateToTestProtocolCommand { get; }
    public ICommand NavigateToCalendarCommand { get; }
    public ICommand NavigateToHistoryCommand { get; }
    public ICommand NavigateToAccountCommand { get; }

    public HomeViewModel()
    {
        NavigateToTestProtocolCommand = new Command(OnNavigateToTestProtocol);
        NavigateToCalendarCommand = new Command(OnNavigateToCalendar);
        NavigateToHistoryCommand = new Command(OnNavigateToHistory);
        NavigateToAccountCommand = new Command(OnNavigateToAccount);
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        // Add any logic that should run when the page appears
    }

    private async void OnNavigateToTestProtocol()
    {
        await Shell.Current.GoToAsync("//TestProtocolPage");
    }

    private async void OnNavigateToCalendar()
    {
        await Shell.Current.GoToAsync("//CalendarPage");
    }

    private async void OnNavigateToHistory()
    {
        await Shell.Current.GoToAsync("//HistoryPage");
    }

    private async void OnNavigateToAccount()
    {
        await Shell.Current.GoToAsync("//AccountPage");
    }
}
