using System;
using System.Globalization;
using System.Windows.Input;
using MyBya.Helpers;
using MyBya.Models.TrainingPlanDetail;
using MyBya.Parameters;
using MyBya.Services.TrainingPlanDetail;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class TrainingScheduleViewModel : ViewModelBase
{
    public ICommand ContinueCommand { get; set; }

    public TrainingScheduleViewModel()
    {
        ContinueCommand = new SyncedCommand(Continue, showLoading: true);
    }

    private async Task Continue(object value)
    {
        if (value is null)
            return;
            
        string convertedDate = DateTime.Parse(value.ToString()).ToString("yyyy-MM-dd");
        
        List<TrainingPlanDetailModel> workouts = await ServiceHelper
            .GetService<TrainingPlanDetailService>()
            .GetTrainingPlansByMemberIdAndDate(memberId: MyByaContext.Instance.GetMemberId(), convertedDate);

        var trainingPlanDetailDTO = new TrainingPlanDetailParameters()
        {
            SelectedDate = value.ToString(),
            Workouts = workouts
        };

        await _navigationService.NavigateToPage<TrainingScheduleResultPage>(trainingPlanDetailDTO);
    }
}