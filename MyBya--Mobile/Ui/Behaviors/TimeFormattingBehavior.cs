using Microsoft.Maui.Controls;

namespace MyBya.Ui.Behaviors
{
    public class TimeFormattingBehavior : Behavior<Entry>
    {
        protected override void OnAttachedTo(Entry entry)
        {
            entry.TextChanged += OnEntryTextChanged;
            base.OnAttachedTo(entry);
        }

        protected override void OnDetachingFrom(Entry entry)
        {
            entry.TextChanged -= OnEntryTextChanged;
            base.OnDetachingFrom(entry);
        }

        private void OnEntryTextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is Entry entry)
            {
                string text = entry.Text ?? string.Empty;
                text = string.Concat(text.Where(char.IsDigit));

                if (text.Length > 2)
                {
                    text = text.Insert(2, ":");
                }

                if (text.Length > 5)
                {
                    text = text.Substring(0, 5);
                }

                if (entry.Text != text)
                {
                    entry.Text = text;
                }
            }
        }
    }
}
