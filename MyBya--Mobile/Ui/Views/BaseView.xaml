﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.BaseView"
             BackgroundColor="#352859">
    <ScrollView>
        <Grid
            RowDefinitions="Auto, *">
            <Image
                Scale="1.2"
                Margin="0,0,0,-1"
                Source="test_protcol_rouding_header.png"/>
            <StackLayout 
                Grid.Row="1"
                Margin="0,0,0,0"
                BackgroundColor="#483778">
                <Grid
                    RowDefinitions="Auto, Auto, *"
                    Margin="0,10,0,0">
                    <Grid>
                        <ImageButton
                            Clicked="navigationIcon_Clicked"
                            Scale="0.7"
                            x:Name="navigationIcon"
                            IsVisible="False"
                            Margin="0,12,0,0"
                            HorizontalOptions="Start"
                            Source="arrow_back.png">
                            <ImageButton.HeightRequest>
                                <OnPlatform x:TypeArguments="x:Double">
                                    <On Platform="iOS" Value="15"/>
                                    <On Platform="Android" Value="40"/>
                                </OnPlatform>
                            </ImageButton.HeightRequest>
                            <ImageButton.WidthRequest>
                                    <OnPlatform x:TypeArguments="x:Double">
                                        <On Platform="iOS" Value="15"/>
                                        <On Platform="Android" Value="40"/>
                                    </OnPlatform>
                            </ImageButton.WidthRequest>  
                        </ImageButton>
                         <Label
                            x:Name="title"
                            Margin="50,20,50,10"
                            FontSize="24"
                            TextColor="#FF7FE8"
                            HorizontalTextAlignment="Center"/>
                    </Grid>
                    <Label
                        TextColor="White"
                        Grid.Row="1"
                        x:Name="subTitle"
                        Margin="10,0,10,0"  
                        FontSize="18"
                        HorizontalTextAlignment="Start"/>
                    <Grid
                        RowDefinitions="*"
                        Margin="0,15,0,0"
                        Grid.Row="2"
                        Padding="10,0,10,0"
                        x:Name="mainContent">
                        <Grid.Background>
                            <LinearGradientBrush>
                                <GradientStop Color="#483778" Offset="0.44"/>
                                <GradientStop Color="#231B3B" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                    </Grid>
                </Grid>
            </StackLayout>
        </Grid>
    </ScrollView>
</ContentView>