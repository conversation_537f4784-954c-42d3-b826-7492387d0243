using System;
using Microsoft.Maui.Controls.Shapes;
using MyBya.Interfaces;

namespace MyBya.Ui.Views.Components;

[ContentProperty(nameof(MainContent))]
public class TopRoundedBorderLayoutView : Border
{
    private Label titleLabel;

    public static readonly BindableProperty MainContentProperty = BindableProperty.Create(
        nameof(MainContent),
        typeof(StackLayout),
        typeof(TopRoundedBorderLayoutView),
        default,
        BindingMode.TwoWay
    );

    public static readonly BindableProperty TitleTextProperty = BindableProperty.Create(
        nameof(TitleText),
        typeof(string),
        typeof(TopRoundedBorderLayoutView),
        default(string),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (TopRoundedBorderLayoutView)bindableObject;
            ctrl.titleLabel.Text = (string)newValue;
        }
    );

    public StackLayout MainContent
	{
		get => (StackLayout)GetValue(MainContentProperty);
		set => SetValue(MainContentProperty, value);
	}

    public string TitleText
    {
        get => (string)GetValue(TitleTextProperty);
        set => SetValue(TitleTextProperty, value);
    }

    public TopRoundedBorderLayoutView()
    {
        BuildLayout();
    }

    public void BuildLayout()
    {
        titleLabel = new Label
        {
            Text = TitleText,
            FontSize = 20,
            Margin = new Thickness(10),
            HorizontalOptions = LayoutOptions.Start,
            TextColor = Color.FromArgb("#D96CC6")
        };



        Image image = new Image();
        image.Scale = 1.2;
        image.Source = "enter_test_data_rouding_header.png";

        BackgroundColor =  Colors.White;
        StrokeShape = new RoundRectangle
        {
            CornerRadius = 5
        };
        
        MainContent = [image, titleLabel];
        Content = MainContent;
    }
}
