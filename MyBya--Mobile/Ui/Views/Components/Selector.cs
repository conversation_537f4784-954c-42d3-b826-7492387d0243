using System;
using System.Collections;
using Microsoft.Maui.Controls.Shapes;
using MyBya.Interfaces;
using MyBya.Ui.Views.Components.Common;

namespace MyBya.Ui.Views.Components;

public class Selector : BaseComponent
{
    private VerticalStackLayout mainStack;
    private Border selector;
    private Label lblSelectedText;
    private ImageButton ic_arrow;
    protected Picker picker;

    public static readonly BindableProperty ItemSourceProperty = BindableProperty.Create(
        nameof(ItemSource),
        typeof(IEnumerable),
        typeof(Selector),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (Selector)bindableObject;
			ctrl.picker.ItemsSource = (IList)newValue;
		}
    );

	public static readonly BindableProperty SelectorBackgroundColorProperty = BindableProperty.Create(
        nameof(SelectorBackgroundColor),
        typeof(Color),
        typeof(Selector),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (Selector)bindableObject;
			ctrl.selector.BackgroundColor = (Color)newValue;
		}
    );

	public static readonly BindableProperty SelectedItemProperty = BindableProperty.Create(
        nameof(SelectedItem),
        typeof(object),
        typeof(Selector),
        default,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (Selector)bindableObject;
            ctrl.picker.SelectedItem = newValue;
        } 
    );

    public static readonly BindableProperty SelectorHorizontalOptionsProperty = BindableProperty.Create(
        nameof(HorizontalOptions),
        typeof(LayoutOptions),
        typeof(Selector),
        default(LayoutOptions),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (Selector)bindableObject;
            ctrl.selector.HorizontalOptions = (LayoutOptions)newValue;
            ctrl.picker.HorizontalOptions = (LayoutOptions)newValue;
        }
    );

    public static readonly BindableProperty SelectorWidthRequestProperty = BindableProperty.Create(
        nameof(WidthRequest),
        typeof(double),
        typeof(Selector),
        default(double),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (Selector)bindableObject;
            ctrl.selector.WidthRequest = (double)newValue;
            ctrl.picker.WidthRequest = (double)newValue;
        }
    );

    public static readonly BindableProperty SelectorHeightRequestProperty = BindableProperty.Create(
        nameof(HeightRequest),
        typeof(double),
        typeof(Selector),
        default(double),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (Selector)bindableObject;
            ctrl.selector.HeightRequest = (double)newValue;
            ctrl.picker.HeightRequest = (double)newValue;
        }
    );

	public IEnumerable ItemSource
	{
		get => (IEnumerable)GetValue(ItemSourceProperty);
		set => SetValue(ItemSourceProperty, value);
	}

	public Color SelectorBackgroundColor
	{
		get => (Color)GetValue(SelectorBackgroundColorProperty);
		set => SetValue(SelectorBackgroundColorProperty, value);
	}

	public object SelectedItem
	{
		get => GetValue(SelectedItemProperty);
		set => SetValue(SelectedItemProperty, value);
	}

    public LayoutOptions SelectorHorizontalOptions
    {
        get => (LayoutOptions)GetValue(SelectorHorizontalOptionsProperty);
        set => SetValue(SelectorHorizontalOptionsProperty, value);
    }

    public double SelectorWidthRequest
    {
        get => (double)GetValue(SelectorWidthRequestProperty);
        set => SetValue(SelectorWidthRequestProperty, value);
    }

    public double SelectorHeightRequest
    {
        get => (double)GetValue(SelectorHeightRequestProperty);
        set => SetValue(SelectorHeightRequestProperty, value);
    }

    private BindingBase itemDisplayBinding;
    public BindingBase ItemDisplayBinding 
    {
        get => itemDisplayBinding;
        set 
        {
            itemDisplayBinding = value;
            picker.ItemDisplayBinding = value;
        }
    }

    public Selector()
    {
        mainStack = new VerticalStackLayout
        {
            Spacing = 10
        };
        mainStack.Add(Title);

        CreateLayout();
    }

    public void CreateLayout()
    {
        var grid = new Grid();
        var innerGrid = new Grid();

        lblSelectedText = new Label
        {
            IsVisible = true,
            Margin = new Thickness(15, 0, 50, 0),
            VerticalTextAlignment = TextAlignment.Center,
            TextColor = Colors.Black
        };
        innerGrid.Add(lblSelectedText);

        ic_arrow = new ImageButton
        {
            Source = "ic_arrow_down.png",
            Scale = 0.25,
            HorizontalOptions = LayoutOptions.End
        };
        innerGrid.Add(ic_arrow);

        selector = new Border
        {
            HorizontalOptions = LayoutOptions.Start,
            Stroke = Colors.Black,
            StrokeShape = new RoundRectangle
            {
                CornerRadius = 4
            },
            InputTransparent = true
        };
        selector.Content = innerGrid;
        grid.Add(selector);

        picker = new Picker();
        picker.HorizontalOptions = LayoutOptions.Start;
        picker.SelectedIndexChanged += SelectedIndex_Changed;
        grid.Add(picker);
        mainStack.Add(grid);

        Content = mainStack;
    }

    public void SetSelectedItem(object selectedItem)
    {
        if (selectedItem is null) 
            return;

        lblSelectedText.Text = selectedItem.ToString() ?? string.Empty;
        SelectedItem = selectedItem;
    }

    private void SelectedIndex_Changed(object? sender, EventArgs e) 
    {
        object selectedItem = ((Picker)sender).SelectedItem;
        SelectedItem = selectedItem is null ? "" : selectedItem;
    }
}
