using System;

namespace MyBya.Ui.Views.Components.Common;

public class BaseComponent : ContentView
{
    protected StackLayout MainContent {get; set;}
    protected Label Title {get; set;}

    public static readonly BindableProperty TitleTextProperty = BindableProperty.Create(
        nameof(TitleText),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.Text = (string)newValue;
		}
    );

    public static readonly BindableProperty TitleFontSizeProperty = BindableProperty.Create(
        nameof(TitleFontSize),
        typeof(double),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.FontSize = (double)newValue;
		}
    );

    public static readonly BindableProperty TitleTextColorProperty = BindableProperty.Create(
        nameof(TitleTextColor),
        typeof(Color),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.TextColor = (Color)newValue;
		}
    );

    public static readonly BindableProperty OrientationProperty = BindableProperty.Create(
        nameof(Orientation),
        typeof(StackOrientation),
        typeof(BaseComponent),
        default(StackOrientation),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (BaseComponent)bindableObject;
            ctrl.MainContent.Orientation = (StackOrientation)newValue;
        }
    );

    public static readonly BindableProperty TitleFontAttributesProperty = BindableProperty.Create(
        nameof(TitleFontAttributes),
        typeof(FontAttributes),
        typeof(BaseComponent),
        FontAttributes.None,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (BaseComponent)bindableObject;
            ctrl.Title.FontAttributes = (FontAttributes)newValue;
        }
    );

    public static readonly BindableProperty SpacingProperty = BindableProperty.Create(
        nameof(Spacing),
        typeof(double),
        typeof(BaseComponent),
        2.0,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) => 
        {
            var ctrl = (BaseComponent)bindableObject;
            ctrl.MainContent.Spacing = (double)newValue;
        }
    );

    public double TitleFontSize
	{
		get => (double)GetValue(TitleFontSizeProperty);
		set => SetValue(TitleFontSizeProperty, value);
	}

    public string TitleText
	{
		get => (string)GetValue(TitleTextProperty);
		set => SetValue(TitleTextProperty, value);
	}

    public Color TitleTextColor
	{
		get => (Color)GetValue(TitleTextColorProperty);
		set => SetValue(TitleTextColorProperty, value);
	}

    public StackOrientation Orientation
    {
        get => (StackOrientation)GetValue(OrientationProperty);
        set => SetValue(OrientationProperty, value);
    }

    public FontAttributes TitleFontAttributes
    {
        get => (FontAttributes)GetValue(TitleFontAttributesProperty);
        set => SetValue(TitleFontAttributesProperty, value);
    }

    public double Spacing
    {
        get => (double)GetValue(SpacingProperty);
        set => SetValue(SpacingProperty, value);
    }

    public BaseComponent()
    {
        CreateMainContent();
        CreateTitle();
        BuildLayout();
        Content = MainContent;
    }

    private void CreateMainContent()
    {
        MainContent = new StackLayout();
        MainContent.Spacing = 5;
    }

    private void CreateTitle()
    {
        Title = new Label();
        MainContent.Add(Title);
    }

    protected virtual void BuildLayout() 
    {
        
    }
}
