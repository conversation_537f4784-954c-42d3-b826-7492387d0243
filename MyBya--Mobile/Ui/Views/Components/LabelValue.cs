using System;
using MyBya.Interfaces;
using MyBya.Ui.Views.Components.Common;

namespace MyBya.Ui.Views.Components;

public class LabelValue : BaseComponent
{
    private Label Value;

     public static readonly BindableProperty LableValueTextProperty = BindableProperty.Create(
        nameof(LableValueText),
        typeof(string),
        typeof(LabelValue),
        default,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelValue)bindableObject;
            ctrl.Value.Text = (string)newValue;
        }
    );

    public string LableValueText
	{
		get => (string)GetValue(LableValueTextProperty);
		set => SetValue(LableValueTextProperty, value);
	}

    public static readonly BindableProperty ValueFontSizeProperty = BindableProperty.Create(
        nameof(ValueFontSize),
        typeof(double),
        typeof(LabelValue),
        default(double),
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelValue)bindableObject;
            ctrl.Value.FontSize = (double)newValue;
        }
    );

    public double ValueFontSize
    {
        get => (double)GetValue(ValueFontSizeProperty);
        set => SetValue(ValueFontSizeProperty, value);
    }

    public static readonly BindableProperty ValueFontAttributesProperty = BindableProperty.Create(
        nameof(ValueFontAttributes),
        typeof(FontAttributes),
        typeof(LabelValue),
        FontAttributes.None,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelValue)bindableObject;
            ctrl.Value.FontAttributes = (FontAttributes)newValue;
        }
    );

    public FontAttributes ValueFontAttributes
    {
        get => (FontAttributes)GetValue(ValueFontAttributesProperty);
        set => SetValue(ValueFontAttributesProperty, value);
    }

    protected override void BuildLayout()
    {
        Value = new Label
        {
            Text = LableValueText,
            FontSize = 17,
            HorizontalOptions = LayoutOptions.Start,
            TextColor = Colors.Black
        };

        MainContent.Add(Value);
    }
}
