﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TrainingScheduleResultPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:TrainingScheduleResultViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="TrainingScheduleResultPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="Training Schedule">
            <views:BaseView.MainContent>
                <ScrollView>
                    <components:TopRoundedBorderLayoutView
                        TitleText="{Binding TitleText}">
                        <Grid
                            RowSpacing="10"
                            RowDefinitions="Auto, Auto, *"
                            Padding="10">
                            <Label
                                Text="{Binding Comment}"
                                FontSize="17"
                                TextColor="Black"/>
                            <Label
                                Margin="0,20,0,0"
                                FontAttributes="Bold"
                                TextColor="Black"
                                Grid.Row="1"
                                Text="Workout instructions"/>
                            <CollectionView
                                ItemsSource="{Binding WorkoutLabels}"
                                Grid.Row="2">
                                <CollectionView.ItemTemplate>
                                    <DataTemplate>
                                        <VerticalStackLayout>
                                            <Label
                                                Text="{Binding WorkoutLabel}"/>
                                        </VerticalStackLayout>
                                    </DataTemplate>
                                </CollectionView.ItemTemplate>
                            </CollectionView>
                        </Grid>
                    </components:TopRoundedBorderLayoutView>
                </ScrollView>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>