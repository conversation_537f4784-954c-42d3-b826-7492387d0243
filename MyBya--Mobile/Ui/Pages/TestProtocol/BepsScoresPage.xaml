﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="MyBya.Ui.Pages.TestProtocol.BepsScoresPage"
        xmlns:views="clr-namespace:MyBya.Ui.Views"
        xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
        x:TypeArguments="viewModels:BepsScoresViewModel"
        xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
        Title="BepsScoresPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            TitleText="BEPS Scores">
            <views:BaseView.MainContent>
                <Label
                    TextColor="White"
                    FontSize="18"
                    Text="Please fill out your BEPS Scores"/>
                <Border
                    Margin="0,20,0,0"
                    VerticalOptions="Center"
                    BackgroundColor="White">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="5"/>
                    </Border.StrokeShape>
                    <VerticalStackLayout>
                        <Image
                            Scale="1.2"
                            Source="enter_test_data_rouding_header.png"/>
                        <Label
                            Margin="10,0,0,0"
                            Text="Test Name"
                            FontSize="20"
                            TextColor="#FF7FE8"/>
                        <CollectionView
                            HorizontalOptions="Start"
                            WidthRequest="250"
                            Margin="10,15,10,0"
                            ItemsSource="{Binding Items}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Grid
                                        Padding="10"
                                        ColumnDefinitions="*">
                                        <Label
                                            FontSize="16"
                                            Text="{Binding Name}"/>
                                        <Label
                                            FontSize="16"
                                            HorizontalTextAlignment="End"
                                            Text="{Binding Value}"/>
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Border>
            </views:BaseView.MainContent>
        </views:BaseView>
        <Button
            Style="{StaticResource DefaultButton}"
            Grid.Row="1"
            VerticalOptions="End"
            Margin="0,0,0,10"   
            CornerRadius="12"
            Text="CONTINUE"
            Command="{Binding ContinueCommand}"/>
    </Grid>
</local:BaseContentPage>