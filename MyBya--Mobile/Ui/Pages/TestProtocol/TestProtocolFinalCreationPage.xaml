<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    x:Class="MyBya.Ui.Pages.TestProtocol.TestProtocolFinalCreationPage"
    x:TypeArguments="viewModels:TestProtocolFinalCreationViewModel"
    Title="Final Test Protocol">
    <ContentPage.Resources>
        <Style TargetType="RadioButton">
            <Setter Property="ControlTemplate">
                <Setter.Value>
                    <ControlTemplate>
                        <Grid ColumnDefinitions="24,*">
                            <Ellipse
                                Stroke="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}"
                                StrokeThickness="2"
                                HeightRequest="20"
                                WidthRequest="20"
                                Fill="Transparent"
                                HorizontalOptions="Center"
                                VerticalOptions="Center" />
                            <Ellipse
                                x:Name="Check"
                                Fill="#1570EF"
                                HeightRequest="10"
                                WidthRequest="10"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Opacity="0" />
                            <Label
                                TextColor="#475467"
                                Grid.Column="2"
                                Text="{TemplateBinding Content}"
                                FontSize="17"
                                VerticalTextAlignment="Center"
                                Margin="5,0,0,0"/>
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CheckedStates">
                                    <VisualState x:Name="Checked">
                                        <VisualState.Setters>
                                            <Setter TargetName="Check" Property="Opacity" Value="1" />
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Unchecked">
                                        <VisualState.Setters>
                                            <Setter TargetName="Check" Property="Opacity" Value="0" />
                                        </VisualState.Setters>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </ContentPage.Resources>
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            VerticalOptions="Fill"
            TitleText="Creating a Test Protocol"
            SubTitleText="The information we are asking you to provide will be used to create the test protocol, biomarker analysis and training plan recommendations tailored specifically to you.">
            <views:BaseView.MainContent>
                <components:TopRoundedBorderLayoutView
                    TitleText="Training Focus">
                    <VerticalStackLayout
                        Padding="10"
                        Spacing="10">
                        <components:LabelValue
                            TitleText="Sport"
                            ValueFontSize="14"
                            LableValueText="{Binding SelectedSport}"/>
                        <components:LabelValue
                            TitleText="Level"
                            ValueFontSize="14"
                            LableValueText="{Binding SelectedLevel}"/>
                        <components:LabelValue
                            TitleText="Event"
                            ValueFontSize="14"
                            LableValueText="{Binding SelectedEvent}"/>
                    </VerticalStackLayout>
                </components:TopRoundedBorderLayoutView>
                <components:TopRoundedBorderLayoutView
                    Margin="0,15,0,0"
                    TitleText="Training Focus">
                    <VerticalStackLayout
                        Padding="10"
                        Spacing="25">
                        <VerticalStackLayout>
                            <Label
                                FontSize="17"
                                Text="Easy run pace (per mile)"/>
                            <HorizontalStackLayout
                                Margin="0,5,0,0"
                                Spacing="10">
                                <components:CEntry
                                    x:Name="entryMinutes"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Minutes}"
                                    Keyboard="Numeric"
                                    BorderColor="Black"
                                    HeightRequest="40"
                                    WidthRequest="65"/>
                                <Label
                                    VerticalTextAlignment="Center"
                                    FontSize="17"
                                    Text="Minutes"/>
                                <components:CEntry
                                    x:Name="entrySeconds"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Seconds}"
                                    Keyboard="Numeric"
                                    BorderColor="Black"
                                    HeightRequest="40"
                                    WidthRequest="65"/>
                                <Label
                                    VerticalTextAlignment="Center"
                                    FontSize="17"
                                    Text="Seconds"/>
                            </HorizontalStackLayout>
                        </VerticalStackLayout>
                        <VerticalStackLayout>
                            <Label
                                FontAttributes="Bold"
                                TextColor="Black"
                                FontSize="17"
                                Text="Test Location"/>
                            <HorizontalStackLayout
                                Spacing="10"
                                Padding="0,10,0,10">
                                <RadioButton 
                                    Value="Treadmill"
                                    Content="Treadmill"
                                    IsChecked="{Binding IsTreadmillSelected}"/>
                                <RadioButton 
                                    Value="Track"
                                    Content="Track"
                                    IsChecked="{Binding IsTrackSelected}"/>
                            </HorizontalStackLayout>
                            <VerticalStackLayout
                                Margin="0,20,0,0">
                                <Label
                                    FontAttributes="Bold"
                                    TextColor="Black"
                                    FontSize="17"
                                    Text="Data you will capture during the test"/>
                                <VerticalStackLayout
                                    Spacing="15"
                                    Padding="0,10,10,10">
                                    <RadioButton 
                                        Value="HeartRateAndLactate"
                                        FontSize="17"
                                        Content="Heart rate and lactate levels (blood test)"
                                        IsChecked="{Binding IsHeartRateAndLactateSelected}"/>
                                    <RadioButton 
                                        Value="HeartRateOnly"
                                        Content="Heart rate only"
                                        IsChecked="{Binding IsHeartRateOnlySelected}"/>
                                </VerticalStackLayout>
                            </VerticalStackLayout>
                        </VerticalStackLayout>
                        <Grid
                            ColumnDefinitions="*,*">
                             <Button
                                Text="BACK"
                                Style="{StaticResource SecondaryButton}"
                                Margin="0,0,10,0"
                                Command="{Binding BackButtonCommand}"/>
                            <Button
                                IsEnabled="{Binding IsFinishButtonEnabled}"
                                Margin="0,0,10,0"   
                                Style="{StaticResource PrimaryButton}"
                                Grid.Column="1"
                                Text="FINISH"
                                Command="{Binding FinishButtonCommand}"/>
                        </Grid>
                    </VerticalStackLayout>
                </components:TopRoundedBorderLayoutView>
            </views:BaseView.MainContent>
        </views:BaseView>    
    </Grid>
</local:BaseContentPage>