<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage 
    x:Class="MyBya.Ui.Pages.AccountPage"
    x:TypeArguments="viewmodels:AccountViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    Title="Account">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Header -->
            <Label 
                Text="Account"
                FontSize="24"
                FontAttributes="Bold"
                TextColor="White"
                HorizontalOptions="Center"
                Margin="0,0,0,20" />

            <!-- User Profile Section -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="20">
                <StackLayout Spacing="10">
                    
                    <!-- Profile Picture Placeholder -->
                    <Frame BackgroundColor="#352859" 
                           CornerRadius="40" 
                           WidthRequest="80" 
                           HeightRequest="80" 
                           HorizontalOptions="Center"
                           Padding="0">
                        <Label 
                            Text="👤"
                            FontSize="40"
                            TextColor="White"
                            HorizontalOptions="Center"
                            VerticalOptions="Center" />
                    </Frame>
                    
                    <!-- User Name -->
                    <Label 
                        Text="{Binding UserName}"
                        FontSize="20"
                        FontAttributes="Bold"
                        TextColor="#352859"
                        HorizontalOptions="Center" />
                    
                    <!-- User Email -->
                    <Label 
                        Text="{Binding UserEmail}"
                        FontSize="14"
                        TextColor="#666"
                        HorizontalOptions="Center" />
                    
                    <!-- Edit Profile Button -->
                    <Button 
                        Text="Edit Profile"
                        BackgroundColor="#352859"
                        TextColor="White"
                        CornerRadius="20"
                        Padding="20,10"
                        FontAttributes="Bold"
                        Command="{Binding EditProfileCommand}"
                        Margin="0,10,0,0" />
                    
                </StackLayout>
            </Frame>

            <!-- Settings Section -->
            <Label 
                Text="Settings"
                FontSize="18"
                FontAttributes="Bold"
                TextColor="White"
                Margin="0,10,0,0" />

            <Frame BackgroundColor="White" CornerRadius="10" Padding="0">
                <StackLayout Spacing="0">
                    
                    <!-- Notification Settings -->
                    <Grid Padding="20,15" ColumnDefinitions="Auto,*,Auto">
                        <Label Grid.Column="0" Text="🔔" FontSize="20" VerticalOptions="Center" />
                        <Label Grid.Column="1" Text="Notifications" FontSize="16" TextColor="#352859" VerticalOptions="Center" Margin="15,0,0,0" />
                        <Label Grid.Column="2" Text=">" FontSize="16" TextColor="#666" VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding NotificationSettingsCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                    
                    <BoxView BackgroundColor="#E0E0E0" HeightRequest="1" Margin="20,0" />
                    
                    <!-- Privacy Policy -->
                    <Grid Padding="20,15" ColumnDefinitions="Auto,*,Auto">
                        <Label Grid.Column="0" Text="🔒" FontSize="20" VerticalOptions="Center" />
                        <Label Grid.Column="1" Text="Privacy Policy" FontSize="16" TextColor="#352859" VerticalOptions="Center" Margin="15,0,0,0" />
                        <Label Grid.Column="2" Text=">" FontSize="16" TextColor="#666" VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding PrivacyPolicyCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                    
                    <BoxView BackgroundColor="#E0E0E0" HeightRequest="1" Margin="20,0" />
                    
                    <!-- Terms of Service -->
                    <Grid Padding="20,15" ColumnDefinitions="Auto,*,Auto">
                        <Label Grid.Column="0" Text="📄" FontSize="20" VerticalOptions="Center" />
                        <Label Grid.Column="1" Text="Terms of Service" FontSize="16" TextColor="#352859" VerticalOptions="Center" Margin="15,0,0,0" />
                        <Label Grid.Column="2" Text=">" FontSize="16" TextColor="#666" VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding TermsOfServiceCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                    
                    <BoxView BackgroundColor="#E0E0E0" HeightRequest="1" Margin="20,0" />
                    
                    <!-- About -->
                    <Grid Padding="20,15" ColumnDefinitions="Auto,*,Auto">
                        <Label Grid.Column="0" Text="ℹ️" FontSize="20" VerticalOptions="Center" />
                        <Label Grid.Column="1" Text="About" FontSize="16" TextColor="#352859" VerticalOptions="Center" Margin="15,0,0,0" />
                        <Label Grid.Column="2" Text=">" FontSize="16" TextColor="#666" VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding AboutCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                    
                </StackLayout>
            </Frame>

            <!-- App Version -->
            <Label 
                Text="{Binding AppVersion, StringFormat='Version {0}'}"
                FontSize="12"
                TextColor="White"
                HorizontalOptions="Center"
                Margin="0,20,0,0" />

            <!-- Logout Button -->
            <Button 
                Text="Logout"
                BackgroundColor="Transparent"
                TextColor="White"
                BorderColor="White"
                BorderWidth="2"
                CornerRadius="25"
                Padding="20,10"
                FontAttributes="Bold"
                Command="{Binding LogoutCommand}"
                Margin="0,20,0,40" />

        </StackLayout>
    </ScrollView>

</pages:BaseContentPage>
