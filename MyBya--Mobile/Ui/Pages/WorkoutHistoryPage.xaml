<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage
    x:Class="MyBya.Ui.Pages.WorkoutHistoryPage"
    x:TypeArguments="viewmodels:WorkoutHistoryViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:converters="clr-namespace:MyBya.Converters"
    Title="Workout History">

    <pages:BaseContentPage.Resources>
        <ResourceDictionary>
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
        </ResourceDictionary>
    </pages:BaseContentPage.Resources>

    <Grid>
        <!-- Content when workouts exist -->
        <ScrollView IsVisible="{Binding HasWorkouts}">
            <StackLayout Padding="20" Spacing="15">
                
                <!-- Header -->
                <Label 
                    Text="Workout History"
                    FontSize="24"
                    FontAttributes="Bold"
                    TextColor="White"
                    HorizontalOptions="Center"
                    Margin="0,0,0,20" />

                <!-- Workout History List -->
                <CollectionView ItemsSource="{Binding WorkoutHistory}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="0,5">
                                <Frame BackgroundColor="White" CornerRadius="10" Padding="15" HasShadow="True">
                                    <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,Auto,Auto">
                                        
                                        <!-- Workout Type -->
                                        <Label 
                                            Grid.Row="0" Grid.Column="0"
                                            Text="{Binding WorkoutType}"
                                            FontSize="18"
                                            FontAttributes="Bold"
                                            TextColor="#352859" />
                                        
                                        <!-- Date -->
                                        <Label 
                                            Grid.Row="0" Grid.Column="1"
                                            Text="{Binding FormattedDate}"
                                            FontSize="14"
                                            TextColor="#666"
                                            VerticalOptions="Center" />
                                        
                                        <!-- Duration -->
                                        <Label 
                                            Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                            Text="{Binding Duration}"
                                            FontSize="14"
                                            TextColor="#666"
                                            Margin="0,5,0,0" />
                                        
                                        <!-- Notes -->
                                        <Label 
                                            Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                            Text="{Binding Notes}"
                                            FontSize="14"
                                            TextColor="#333"
                                            Margin="0,5,0,0" />
                                        
                                    </Grid>
                                </Frame>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

            </StackLayout>
        </ScrollView>

        <!-- Empty state when no workouts -->
        <StackLayout IsVisible="{Binding HasWorkouts, Converter={StaticResource InvertedBoolConverter}}" 
                     VerticalOptions="Center" 
                     HorizontalOptions="Center" 
                     Padding="40">
            
            <Label 
                Text="📊"
                FontSize="60"
                HorizontalOptions="Center"
                Margin="0,0,0,20" />
            
            <Label 
                Text="No Workout History"
                FontSize="24"
                FontAttributes="Bold"
                TextColor="White"
                HorizontalOptions="Center"
                Margin="0,0,0,10" />
            
            <Label 
                Text="Complete your first test protocol or training session to see your workout history here."
                FontSize="16"
                TextColor="White"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Margin="0,0,0,30" />
            
            <Button 
                Text="Start Test Protocol"
                BackgroundColor="White"
                TextColor="#352859"
                CornerRadius="25"
                Padding="20,10"
                FontAttributes="Bold"
                Command="{Binding NavigateToTestProtocolCommand}" />
            
        </StackLayout>

    </Grid>

</pages:BaseContentPage>
