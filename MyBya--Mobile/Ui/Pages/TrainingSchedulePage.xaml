﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TrainingSchedulePage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:TrainingScheduleViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="TrainingSchedulePage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            TitleText="Training Schedule">
            <views:BaseView.MainContent>
                <Grid
                    RowDefinitions="*"
                    ColumnDefinitions="*">
                    <DatePicker
                        VerticalOptions="Center"
                        DateSelected="datePicker_DateSelected"
                        x:Name="datePicker"/>
                    <components:Selector
                        SelectorHorizontalOptions="Fill"
                        x:Name="selector"
                        InputTransparent="True"
                        SelectorBackgroundColor="White"
                        TitleTextColor="White"
                        TitleText="Select Date"/>
                </Grid>
            </views:BaseView.MainContent>
        </views:BaseView>
        <Button
            Style="{StaticResource DefaultButton}"
            Margin="0,0,0,20"
            x:Name="btnAddTraining"
            Text="Continue"
            Command="{Binding ContinueCommand}"
            CommandParameter="{Binding Source={x:Reference selector}, Path=SelectedItem}"
            HorizontalOptions="Center"
            VerticalOptions="End" />
    </Grid>
</local:BaseContentPage>    