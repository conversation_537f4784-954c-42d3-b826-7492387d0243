﻿using MyBya.DbContext.Handler;
using MyBya.DbContext.Handler.Interface;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Ui.Pages;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;
using Serilog;

namespace MyBya;

public partial class App : Application  
{
  public App()
  {
    InitializeComponent();
    InitLogging();
    //MainPage = new NavigationPage(new TestProtocolSetupPage(new TestProtocolSetupViewModel()));
    //MainPage = new NavigationPage(new TrainingTargetsPage(new TrainingTargetsViewModel()));
    //MainPage = new NavigationPage(new TrainingSchedulePage(new TrainingScheduleViewModel()));
    //MainPage = new NavigationPage(new TrainingScheduleResultPage(new TrainingScheduleResultViewModel()));
    MainPage = new NavigationPage(new TestProtocolInitialCreationPage(new TestProtocolInitialCreationViewModel()));
    //MainPage = new NavigationPage(new YourTestProtocolPage(new YourTestProtocolViewModel()));
    //MainPage = new NavigationPage(new EnterTestDataPage(new EnterTestDataViewModel()));
    //MainPage = new NavigationPage(new TrainingTargetsPage(new TrainingTargetsViewModel()));
    }
    
  private static void InitLogging()
  {
    ServiceHelper.GetService<ILogService>().Initialize();
  }
}