using MyBya.Enums;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class TestProtocolParameters
{
    public SportEnum Sport { get; set; }
    public LevelEnum Level { get; set; }
    public string Event { get; set; }
    public string Time { get; set; }
    public IntervalTypeEnum IntervalType { get; set; }
    public int IntervalDistance { get; set; }
    public bool IsTreadmill { get; set; }
    public bool IsTrack { get; set; }
    public bool IsHeartRateAndLactate { get; set; }
    public bool IsHeartRateOnly { get; set; }
}
