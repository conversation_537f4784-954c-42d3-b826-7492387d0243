using System;

namespace MyBya.Models.Api;

public class ErrorDetails
{
    public string Code { get; set; }
    public string Message { get; set; }
    public string Details { get; set; }
    public DataModel Data { get; set; }
    public List<ValidationErrorModel> ValidationErrors { get; set; }
}

public class DataModel
{
    public string AdditionalProp1 { get; set; }
    public string AdditionalProp2 { get; set; }
    public string AdditionalProp3 { get; set; }
}

public class ValidationErrorModel
{
    public string Message { get; set; }
    public List<string> Members { get; set; }
}
