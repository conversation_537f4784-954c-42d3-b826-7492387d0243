using System;
using MyBya.Models.Common;

namespace MyBya.Models.Members;

public class MemberModel : BaseModel
{
     public int UserId { get; set; }
    public int TeamId { get; set; }
    public int Role { get; set; }
    public string StripeCustomerId { get; set; }
    public int? GroupId { get; set; }
    public bool CoachAccess { get; set; }
    public bool DataEntryAccess { get; set; }
    public bool IsDeleted { get; set; }
    public int? DeleterId { get; set; }
    public DateTime? DeletionTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public int? LastModifierId { get; set; }
    public DateTime CreationTime { get; set; }
    public int? CreatorId { get; set; }
}
