using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models.Athletes;

public class AthleteModel : BaseModel
{
    public int MemberId { get; set; }
    public int UserId { get; set; }
    public string LastName { get; set; }
    public string FirstName { get; set; }
    [JsonIgnore]
    public string FullName { get; set; }

    public void SetFullName()
    {
        if (!string.IsNullOrEmpty(FirstName))
            FullName = FirstName;
        
        if (!string.IsNullOrEmpty(LastName))
            FullName += " " + LastName;
    }
}
