using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models.AthleteTestSetup;

public class AthleteTestSetupModel : BaseModel
{
    public int Id { get; set; }
    public DateTime? CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
    public int? TestDetailAthleteId { get; set; }
    public int? TestCalendarId { get; set; }
    public int? Sport { get; set; }
    public string Time { get; set; }
    public int? DataType { get; set; }
    public int? IntervalType { get; set; }
    public int? Level { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}