using MyBya.Constants;
using MyBya.Models.BepsTestData;
using MyBya.Services.Common;
using MyBya.Services.DTOs.BepsTestData;
using Newtonsoft.Json;

namespace MyBya.Services.BepsTestData;

public class BepsTestDataService : BaseService<BepsTestDataModel, BepsTestDataResponseDto>
{
    public BepsTestDataService()
    {
        apiUrl = ApiKeys.GetURL("BepsTestDataURL");
    }

    public async Task<BepsTestDataModel?> CreateBepsResult(int? testDetailAthleteId) 
    {
        var bepsTestData = new BepsTestDataDto
        {
            TestDetailAthleteId = testDetailAthleteId.Value
        };
        var dto = await PostRequestWithModel(JsonConvert.SerializeObject(bepsTestData));
        return Mapper.Map<BepsTestDataModel>(dto);
    }
}
