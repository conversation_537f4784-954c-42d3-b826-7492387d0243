using System;
using MyBya.Constants;
using MyBya.Models.TestProtocolInstruction;
using MyBya.Services.Common;
using MyBya.Services.Entities.TestProtocolInstruction;

namespace MyBya.Services.TestProtocolInstruction;

public class TestProtocolInstructionService : BaseService<TestProtocolInstructionModel, TestProtocolInstructionDto>
{
    public async Task<List<TestProtocolInstructionModel>> GetTestProtocolInstructions(int sport,
        int intervalType,
        int intervalDistance)
    {
        apiUrl = string.Format(
            ApiKeys.GetURL("TestProtocolInstructionBySportURL"),
            sport,
            intervalType
        );

        if (intervalType == 0)
            apiUrl += $"&intervalDistance={intervalDistance}";

        var models = new List<TestProtocolInstructionModel>();
        var dtos = await GetListRequestWithModel();

        foreach (var dto in dtos)
        {
            var model = Mapper.Map<TestProtocolInstructionModel>(dto);
            models.Add(model);
        }
        
        return models;
    }
}
