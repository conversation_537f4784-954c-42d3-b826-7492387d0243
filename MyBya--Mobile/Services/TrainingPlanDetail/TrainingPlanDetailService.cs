﻿using System;
using MyBya.Constants;
using MyBya.Models.TrainingPlanDetail;
using MyBya.Services.Common;
using MyBya.Services.DTOs.TrainingPlanDetail;

namespace MyBya.Services.TrainingPlanDetail
{
	public class TrainingPlanDetailService : BaseService<TrainingPlanDetailModel, TrainingPlanDetailDto>
	{
		public TrainingPlanDetailService()
		{
			apiUrl = ApiKeys.GetURL("TrainingPlanDetailURL");
		}

		public async Task<List<TrainingPlanDetailModel>> GetTrainingPlansByMemberIdAndDate(int memberId, string date)
		{
			apiUrl += memberId + "?date=" + date; 

			var dtos = await GetListRequestWithModel();
			var models = new List<TrainingPlanDetailModel>();

			foreach (var dto in dtos)
			{
				var model = Mapper.Map<TrainingPlanDetailModel>(dto);
				models.Add(model);
			}

			return models;
		}
	}
}