using System;
using MyBya.Constants;
using MyBya.Models.TrainingPlanEvent;
using MyBya.Services.Common;
using MyBya.Services.DTOs.TrainingPlanEvent;

namespace MyBya.Services.TrainingPlanEvent;

public class TrainingPlanEventService : BaseService<TrainingPlanEventModel, TrainingPlanEventDto>
{
    public TrainingPlanEventService()
    {
        apiUrl = ApiKeys.GetURL("TrainingPlanEventURL");
    }

    public async Task<List<TrainingPlanEventModel>> GetTrainingPlanEvents()
    {
        apiUrl = string.Format(ApiKeys.GetURL("TrainingPlanEventURL"), "");

        var dtos = GetApiResponseList<TrainingPlanEventDto>(await GetRequest());
        var models = new List<TrainingPlanEventModel>();

        foreach (var dto in dtos)
        {
            var model = Mapper.Map<TrainingPlanEventModel>(dto);
            models.Add(model);
        }

        return models;
    }

    public async Task<List<TrainingPlanEventModel>> GetTrainingPlanEventBySport(string sport)
    {
        apiUrl = string.Format(ApiKeys.GetURL("TrainingPlanEventBySportURL"), sport);

        var dtos = GetApiResponseList<TrainingPlanEventDto>(await GetRequest());
        var models = new List<TrainingPlanEventModel>();

        foreach (var dto in dtos)
        {
            var model = Mapper.Map<TrainingPlanEventModel>(dto);
            models.Add(model);
        }

        return models;
    }
}