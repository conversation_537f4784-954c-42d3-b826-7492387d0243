using System;
using MyBya.Constants;
using MyBya.Models.TestDataEntry;
using MyBya.Services.Common;
using MyBya.Services.DTOs.TestDataEntry;
using Newtonsoft.Json;

namespace MyBya.Services.TestDataEntry;

public class TestDataEntryService : BaseService<TestDataEntryModel, TestDataEntryDto>
{
    public TestDataEntryService()
    {
        apiUrl = ApiKeys.GetURL("TestDataEntryURL");
    }

    public async Task<TestDataEntryModel?> SendTestDataEntry(TestDataEntryDto testDataEntryEntity) 
    {
        var dto = await PostRequestWithModel(JsonConvert.SerializeObject(testDataEntryEntity));
        return Mapper.Map<TestDataEntryModel>(dto);
    }
}
