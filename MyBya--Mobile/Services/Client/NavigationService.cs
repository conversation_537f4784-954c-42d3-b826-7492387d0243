using System;
using MyBya.Interfaces;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Services.Client;

public class NavigationService : INavigationService
{
    private readonly IServiceProvider _services;

    protected INavigation Navigation
    {
        get
        {
            INavigation? navigation = Application.Current?.MainPage?.Navigation;

            if (navigation is not null)
                return navigation;

            return null;
        }
    }

    public NavigationService(IServiceProvider services)
        => _services = services;

    public async Task NavigateBack()
    {
        if (Navigation.NavigationStack.Count >= 1)
        {
            await Navigation.PopAsync();
            return;
        }

        throw new InvalidOperationException("No pages to navigate back to!");
    }

    public async Task NavigateToPage<T>(object? parameter = null) where T : Page
    {
        var toPage = ResolvePage<T>();

        if (toPage is not null)
        {
            //Subscribe to the toPage's NavigatedTo event
            toPage.NavigatedTo += Page_NavigatedTo;   

            //Get VM of the toPage
            ViewModelBase? toViewModel = GetPageViewModelBase(toPage);

            //Call navigatingTo on VM, passing in the paramter
            if (toViewModel is not null)
                await toViewModel.OnNavigatingTo(parameter);

            //Navigate to requested page
            await Navigation.PushAsync(toPage, true);

            //Subscribe to the toPage's NavigatedFrom event
            toPage.NavigatedFrom += Page_NavigatedFrom;

            try
            {
                if (parameter != null && Convert.ToBoolean(parameter))
                {
                    Application.Current.Dispatcher.Dispatch(() =>
                    {
                        App.Current.MainPage = new NavigationPage(toPage);
                    });
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
            }

            return;
        }

        throw new InvalidOperationException($"Unable to resolve type {typeof(T).FullName}");
    }

    public async Task CloseCurrentAndNavigateToPage<T>(object? parameter = null) where T : Page
    {
        int pageCount = Navigation.NavigationStack.Count;
        var pagetoRemove = Navigation.NavigationStack[pageCount - 1];
        await NavigateToPage<T>(parameter);
        Navigation.RemovePage(pagetoRemove);
    }

    private async void Page_NavigatedFrom(object? sender, NavigatedFromEventArgs e)
    {
        //To determine forward navigation, we look at the 2nd to last item on the NavigationStack
        //If that entry equals the sender, it means we navigated forward from the sender to another page
        bool isForwardNavigation = Navigation.NavigationStack.Count > 1
            && Navigation.NavigationStack[^2] == sender;

        if (sender is Page thisPage)
        {
            if (!isForwardNavigation)
            {
                thisPage.NavigatedTo -= Page_NavigatedTo;
                thisPage.NavigatedFrom -= Page_NavigatedFrom;
            }

            await CallNavigatedFrom(thisPage, isForwardNavigation);
        }
    }

    private Task CallNavigatedFrom(Page p, bool isForward)
    {
        var fromViewModel = GetPageViewModelBase(p);

        if (fromViewModel is not null)
            return fromViewModel.OnNavigatedFrom(isForward);
        return Task.CompletedTask;
    }

    private async void Page_NavigatedTo(object? sender, NavigatedToEventArgs e)
        => await CallNavigatedTo(sender as Page);

    private Task CallNavigatedTo(Page? p)
    {
        var fromViewModel = GetPageViewModelBase(p);

        if (fromViewModel is not null)
            return fromViewModel.OnNavigatedTo();

        return Task.CompletedTask;
    }

    private ViewModelBase? GetPageViewModelBase(Page? p)
        => p?.BindingContext as ViewModelBase;

    private T? ResolvePage<T>() where T : Page
        => _services.GetService<T>();

    public async Task CloseModal()
    {
        await Navigation.PopModalAsync();
    }

    public async Task CloseAll()
    {
        await Navigation.PopToRootAsync();
    }

    public async Task NavigateToModal<T>(object parameter = null) where T : Page
    {
        var toPage = ResolvePage<T>();

        if (toPage is not null)
        {
            //Subscribe to the toPage's NavigatedTo event
            toPage.NavigatedTo += Page_NavigatedTo;

            //Get VM of the toPage
            var toViewModel = GetPageViewModelBase(toPage);

            //Call navigatingTo on VM, passing in the paramter
            if (toViewModel is not null)
                await toViewModel.OnNavigatingTo(parameter);

            //Navigate to requested page
            await Navigation.PushModalAsync(toPage, true);

            //Subscribe to the toPage's NavigatedFrom event
            toPage.NavigatedFrom += Page_NavigatedFrom;
        }
        else
            throw new InvalidOperationException($"Unable to resolve type {typeof(T).FullName}");
    }

    public async Task CloseCurrentAndNavigateToModal<T>(object parameter = null) where T : Page
    {
        await Navigation.PopModalAsync();
        await NavigateToModal<T>();
    }
}
