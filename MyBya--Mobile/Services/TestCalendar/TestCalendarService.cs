using MyBya.Constants;
using MyBya.Models.TestCalendar;
using MyBya.Services.Common;
using MyBya.Services.DTOs.TestCalendar;

namespace MyBya.Services.TestCalendar;

public class TestCalendarService : BaseService<TestCalendarModel, TestCalendarDto>
{
    public TestCalendarService()
    {
        apiUrl = ApiKeys.GetURL("TestCalendarURL");
    }

    public async Task<TestCalendarModel?> GetTestCalendar(int testCalendarId) 
    {
        apiUrl = string.Format(apiUrl, testCalendarId); 
        var dto = await GetRequestWithModel();
        return Mapper.Map<TestCalendarModel>(dto);
    }
}
