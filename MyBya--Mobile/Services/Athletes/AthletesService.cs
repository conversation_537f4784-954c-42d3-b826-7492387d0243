using System;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models.Athletes;
using MyBya.Services.Common;
using MyBya.Services.DTOs.Athletes;

namespace MyBya.Services.Athletes;

public class AthletesService : BaseService<AthleteModel, AthleteDto>
{
    public AthletesService()
    {
        apiUrl = ApiKeys.GetURL("AthletesServiceURL");
    }

    public async Task<List<AthleteModel>> GetAthletes(int max = 10) 
    {
        apiUrl = string.Format(apiUrl, max);

        var athletes = new List<AthleteModel>();
        var response = GetApiResponseList<AthleteDto>(await GetRequest());

        foreach (AthleteDto item in response) 
        {
            var athlete = Mapper.Map<AthleteModel>(item);
            athlete.SetFullName();
            athletes.Add(athlete);
        }

        return athletes.Where(x => 
            !string.IsNullOrEmpty(x.FirstName) && 
            !string.IsNullOrEmpty(x.LastName)).ToList();
    }
}
