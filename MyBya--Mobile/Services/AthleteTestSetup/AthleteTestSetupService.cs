using System;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models.AthleteTestSetup;
using MyBya.Services.Common;
using MyBya.Services.DTOs.AthleteTestSetup;
using Newtonsoft.Json;

namespace MyBya.Services.AthleteTestSetup;

public class AthleteTestSetupService : BaseService<AthleteTestSetupModel, AthleteTestSetupResponseDto>
{
    public AthleteTestSetupService()
    {
        apiUrl = ApiKeys.GetURL("TestSetupCalendarURL");
    }

    public async Task<AthleteTestSetupModel?> CreateAthleteTest(AthleteTestSetupDto item)
    {
        var dto = await PostRequestWithModel(JsonConvert.SerializeObject(item));
        return Mapper.Map<AthleteTestSetupModel>(dto);
    }
}