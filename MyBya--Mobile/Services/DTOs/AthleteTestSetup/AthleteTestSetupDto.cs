using MyBya.Enums;
using MyBya.Services.Common;
using Newtonsoft.Json;

namespace MyBya.Services.DTOs.AthleteTestSetup;

public class AthleteTestSetupDto : BaseDto
{
    public SportEnum Sport { get; set; }
    public string Time { get; set; }
    public LevelEnum Level { get; set; }
    public string Goal { get; set; } = "00:00";

    [JsonProperty("user_id")]
    public int UserId { get; set; }

    [JsonProperty("data_type")]
    public int DataType { get; set; }

    [JsonProperty("event_id")]
    public int EventId { get; set; } 

    [JsonProperty("interval_type")]
    public IntervalTypeEnum IntervalType { get; set; }
}