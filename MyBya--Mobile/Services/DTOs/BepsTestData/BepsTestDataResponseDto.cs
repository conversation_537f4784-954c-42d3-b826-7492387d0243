using System;
using System.Text.Json.Serialization;

namespace MyBya.Services.DTOs.BepsTestData;

public class BepsTestDataResponseDto : BepsTestDataDto
{
    [JsonPropertyName("af")]
    public double? Af { get; set; }

    [JsonPropertyName("pac")]
    public double? Pac { get; set; }

    [JsonPropertyName("ltcc")]
    public double? Ltcc { get; set; }

    [JsonPropertyName("arc3")]
    public double? Arc3 { get; set; }

    [JsonPropertyName("arc2")]
    public double? Arc2 { get; set; }

    [JsonPropertyName("arc1")]
    public double? Arc1 { get; set; }

    [JsonPropertyName("anrc2")]
    public double? Anrc2 { get; set; }

    [JsonPropertyName("anrc1")]
    public double? Anrc1 { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime? UpdatedAt { get; set; }
}
