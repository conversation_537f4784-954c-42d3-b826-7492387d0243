using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using MyBya.Helpers;
using MyBya.Models.Api;
using MyBya.Models.Common;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services.Common;

public abstract class BaseService<T, E> where T : BaseModel where E : BaseDto
{
    private HttpClient _client { get; set; }
    protected string apiUrl { get; set; }
    protected IMapper Mapper { get; set; }

    public BaseService()
    {
#if DEBUG
        var handlerDebug = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
        };

        _client = new HttpClient(handlerDebug)
        {
            BaseAddress = new Uri("https://localhost:44365")
        };
#else
        var handler = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
        };

        _client = new HttpClient(handler);
#endif

        //_client = new HttpClient();
        Mapper = ServiceHelper.GetService<IMapper>();
    }

    protected async Task<E?> GetRequestWithModel()
    {
        try
        {
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponseWithModel(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<List<E>> GetListRequestWithModel()
    {
        try
        {
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonListResponseWithModel(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<string> GetRequest()
    {
        try
        {
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<E?> PostRequestWithModel(string contentJson)
    {
        try
        {
            StringContent content = GetStringContent(contentJson);
            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    return await GetJsonResponseWithModel(response);

                case HttpStatusCode.BadRequest:
                    return default;
            }

            throw new Exception($"Error on post request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<string> PostRequest(string contentJson)
    {
        try
        {
            StringContent content = GetStringContent(contentJson);
            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.BadRequest:
                    return default;
            }

            throw new Exception($"Error on post request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<string> PutRequest(string? contentJson = null)
    {
        try
        {
            StringContent? content = contentJson is null ? null : GetStringContent(contentJson);
            HttpResponseMessage response = await _client.PutAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.Unauthorized:
                    response = await _client.PutAsync(apiUrl, content);
                    return await GetJsonResponse(response);
            }

            throw new Exception($"Error on Put request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<string> DeleteRequest(string contentJson)
    {
        try
        {
            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Delete,
                RequestUri = new Uri(apiUrl),
                Content = GetStringContent(contentJson)
            };

            var response = await _client.SendAsync(request);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.Unauthorized:
                    HttpRequestMessage refreshRequest = RefreshDeleteRequest(contentJson);
                    response = await _client.SendAsync(refreshRequest);
                    return await GetJsonResponse(response);
            }

            throw new Exception($"Error on Delete request, status code: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw ex;
        }
    }

    protected async Task<string> UploadFile(byte[] content, string fileName)
    {
        using var formData = new MultipartFormDataContent();
        var fileStream = new MemoryStream(content);
        var fileContent = new StreamContent(fileStream);
        fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
        formData.Add(fileContent, "file", fileName);

        var response = await _client.PostAsync(apiUrl, formData);

        switch (response.StatusCode)
        {
            case HttpStatusCode.Created:
            case HttpStatusCode.OK:
                return await GetJsonResponse(response);

            case HttpStatusCode.Unauthorized:
                using (var newFormData = new MultipartFormDataContent())
                {
                    var newFileStream = new MemoryStream(content);
                    var newFileContent = new StreamContent(newFileStream);
                    newFileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    newFormData.Add(newFileContent, "file", fileName);
                    response = await _client.PostAsync(apiUrl, newFormData);
                }
                return await GetJsonResponse(response);
        }

        string errorMessage = await GetJsonResponse(response);
        throw new Exception($"Error on uploading image, error: {errorMessage}, status code: {response.StatusCode}");
    }

    protected StringContent GetStringContent(string content)
    {
        return new StringContent(
            content: content,
            encoding: Encoding.UTF8,
            mediaType: "application/json"
        );
    }

    protected virtual string SerializeEntity<EE>(EE instance)
    {
        return JsonConvert.SerializeObject(instance);
    }

    protected virtual E GetEntity(T model)
    {
        throw new NotSupportedException("Derived classes must implement GetEntity");
    }

    private async Task<string> GetJsonResponse(HttpResponseMessage response)
    {
        string jsonResponse = await response.Content.ReadAsStringAsync();
#if DEBUG
        Debug.WriteLine($" ====> API RESPONSE: {jsonResponse} <=====");
#endif
        return jsonResponse;
    }

    private async Task<E?> GetJsonResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse)) 
        {
            E? model = JsonConvert
                .DeserializeObject<E>(jsonResponse);

            return model;
        }

        return null;
    }

    private async Task<List<E>> GetJsonListResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse))
        {
            List<E> model = JsonConvert
                .DeserializeObject<List<E>>(jsonResponse);

            return model;
        }

        return null;
    }

    protected virtual List<E> GetApiResponseList<E>(string json)
        where E : new()
    {
        ApiResponseList<E>? apiResponse = JsonConvert
            .DeserializeObject<ApiResponseList<E>>(json);

        return apiResponse.Items;
    }

    private HttpRequestMessage RefreshDeleteRequest(string contentJson)
    {
        var refreshRequest = new HttpRequestMessage();
        refreshRequest.Method = HttpMethod.Delete;
        refreshRequest.RequestUri = new Uri(apiUrl);
        refreshRequest.Content = GetStringContent(contentJson);
        return refreshRequest;
    }
}   
