using System;
using CommunityToolkit.Maui;
using MyBya.AutoMapper;
using MyBya.DataAccess;
using MyBya.DataAccess.Interfaces;
using MyBya.DbContext.Handler;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Interfaces;
using MyBya.Logging;
using MyBya.Models.TestResultHeartRate;
using MyBya.Models.TestResultPaceChart;
using MyBya.Services.AppUser;
using MyBya.Services.Athletes;
using MyBya.Services.AthleteTestSetup;
using MyBya.Services.BepsTestData;
using MyBya.Services.Client;
using MyBya.Services.Members;
using MyBya.Services.TestCalendar;
using MyBya.Services.TestDataEntry;
using MyBya.Services.TestProtocolInstruction;
using MyBya.Services.TestResult;
using MyBya.Services.TestResultAnrc;
using MyBya.Services.TestResultHeartRate;
using MyBya.Services.TestResultPaceChart;
using MyBya.Services.TrainingPlanDetail;
using MyBya.Services.TrainingPlanEvent;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;

namespace MyBya.Extensions;

public static class MauiAppBuilderExtension
{
    public static MauiAppBuilder AddDependencies(this MauiAppBuilder builder)
    {
        AddSingletons(builder);
        AddPages(builder);
        AddViewModels(builder);
        AddServices(builder);
        AddControls(builder);
        AddAutoMapper(builder);
        AddViews(builder);
		AddRepositories(builder);
		AddDbContext(builder);
        return builder;
    }

	private static void AddDbContext(MauiAppBuilder builder)
	{
		builder.Services.AddSingleton(typeof(IDatabaseHandler<TestResultPaceChartEntity>), typeof(DatabaseHandler<TestResultPaceChartEntity>));
		builder.Services.AddSingleton(typeof(IDatabaseHandler<TestResultHeartRatesEntity>), typeof(DatabaseHandler<TestResultHeartRatesEntity>));
    }

    private static void AddRepositories(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<ITestResultHeartRateRepository, TestResultHeartRateRepository>();
		builder.Services.AddTransient<ITestResultPaceChartRepository, TestResultPaceChartRepository>();
    }

    private static void AddViews(MauiAppBuilder builder)
    {
		builder.Services.AddTransient<LoadingView>();
    }

    private static void AddAutoMapper(MauiAppBuilder builder)
    {
        builder.Services.AddAutoMapper(typeof(ConfigurationMapper));
    }

    private static void AddControls(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<Spinner>();
	}

	private static void AddSingletons(MauiAppBuilder builder)
	{
		builder.Services.AddSingleton<IAlertService, AlertService>();
		builder.Services.AddSingleton<INavigationService, NavigationService>();
	}

	private static void AddPages(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<TestProtocolSetupPage>();
		builder.Services.AddTransient<EnterTestDataPage>();
		builder.Services.AddTransient<BepsScoresPage>();
		builder.Services.AddTransient<TrainingTargetsPage>();
		builder.Services.AddTransient<TrainingSchedulePage>();
		builder.Services.AddTransient<TrainingScheduleResultPage>();
		builder.Services.AddTransient<TestProtocolInitialCreationPage>();
		builder.Services.AddTransient<TestProtocolFinalCreationPage>();
		builder.Services.AddTransient<YourTestProtocolPage>();
	}

	private static void AddViewModels(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<TestProtocolSetupViewModel>();
		builder.Services.AddTransient<EnterTestDataViewModel>();
		builder.Services.AddTransient<BepsScoresViewModel>();
		builder.Services.AddTransient<TrainingTargetsViewModel>();
		builder.Services.AddTransient<TrainingScheduleViewModel>();
		builder.Services.AddTransient<TrainingScheduleResultViewModel>();
		builder.Services.AddTransient<TestProtocolInitialCreationViewModel>();
		builder.Services.AddTransient<TestProtocolFinalCreationViewModel>();
		builder.Services.AddTransient<YourTestProtocolViewModel>();
	}

	private static void AddServices(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<ILogService, LogService>();
		builder.Services.AddTransient<MembersService>();
		builder.Services.AddTransient<AppUserService>();
		builder.Services.AddTransient<AthletesService>();
		builder.Services.AddTransient<AthleteTestSetupService>();
		builder.Services.AddTransient<TestCalendarService>();
		builder.Services.AddTransient<TestDataEntryService>();
		builder.Services.AddTransient<BepsTestDataService>();
		builder.Services.AddTransient<TestResultByTestDetailAthleteIdService>();
		builder.Services.AddTransient<TestResultHeartRateService>();
		builder.Services.AddTransient<TestResultPaceChartService>();
		builder.Services.AddTransient<TestResultAnrcService>();
		builder.Services.AddTransient<TrainingPlanDetailService>();
		builder.Services.AddTransient<TrainingPlanEventService>();
		builder.Services.AddTransient<TestProtocolInstructionService>();
	}
}