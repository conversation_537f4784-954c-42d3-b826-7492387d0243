using System;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.TestCalendar;

namespace MyBya.Shared;

public sealed class MyByaContext
{
    private static readonly Lazy<MyByaContext> _instance = new(() => new MyByaContext());
    public static MyByaContext Instance => _instance.Value;

    private int _testDetailAthleteId { get; set; }
    private int _memberId { get; set; } 

    public TestCalendarModel CurrentTestCalendar { get; set; }
    public AthleteTestSetupModel CurrentTestSetup { get; set; }

    public void SetCurrentTestCalendar(TestCalendarModel testCalendar)
    {
        CurrentTestCalendar = testCalendar;
    }

    public void SetCurrentTestSetup(AthleteTestSetupModel testSetup)
    {
        CurrentTestSetup = testSetup;
        SetTestDetailAthleteId(testSetup.TestDetailAthleteId.Value);
    }   

    public void SetMemberId(int id)
    {
        _memberId = id;
    }
  
    public void SetTestDetailAthleteId(int id)
    {
        _testDetailAthleteId = id;
    }

    public int GetTestDetailAthleteId()
    {
        return _testDetailAthleteId;
    }

    public int GetMemberId() 
    {
        return _memberId;
    }
}
