using System;
using AutoMapper;
using MyBya.Entities;
using MyBya.Models;
using MyBya.Models.Athletes;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.BepsTestData;
using MyBya.Models.Members;
using MyBya.Models.TestCalendar;
using MyBya.Models.TestDataEntry;
using MyBya.Models.TestProtocolInstruction;
using MyBya.Models.TestResultAnrc;
using MyBya.Models.TestResultHeartRate;
using MyBya.Models.TestResultPaceChart;
using MyBya.Models.TrainingPlanDetail;
using MyBya.Models.TrainingPlanEvent;
using MyBya.Services.DTOs;
using MyBya.Services.DTOs.Athletes;
using MyBya.Services.DTOs.AthleteTestSetup;
using MyBya.Services.DTOs.BepsTestData;
using MyBya.Services.DTOs.Members;
using MyBya.Services.DTOs.TestCalendar;
using MyBya.Services.DTOs.TestDataEntry;
using MyBya.Services.DTOs.TestResultAnrc;
using MyBya.Services.DTOs.TrainingPlanDetail;
using MyBya.Services.DTOs.TrainingPlanEvent;
using MyBya.Services.Entities.TestProtocolInstruction;

namespace MyBya.AutoMapper;

public class ConfigurationMapper : Profile
{
    public ConfigurationMapper()
    {
        CreateMap<AthleteModel, AthleteDto>();
        CreateMap<AppUserModel, AppUserDto>();
        CreateMap<AthleteTestSetupResponseDto, AthleteTestSetupModel>();
        CreateMap<BepsTestDataResponseDto, BepsTestDataModel>();
        CreateMap<MemberServiceDto, MemberModel>();
        CreateMap<TestResultHeartRateModel, TestResultHeartRatesEntity>();
        CreateMap<TestResultPaceChartModel, TestResultPaceChartEntity>();
        CreateMap<TestCalendarDto, TestCalendarModel>();
        CreateMap<TestDataEntryDto, TestDataEntryModel>();
        CreateMap<TestResultAnrcDto, TestResultAnrcModel>();
        CreateMap<TrainingPlanDetailDto, TrainingPlanDetailModel>();
        CreateMap<TrainingPlanEventDto, TrainingPlanEventModel>();
        CreateMap<TestProtocolInstructionDto, TestProtocolInstructionModel>();
    }
}