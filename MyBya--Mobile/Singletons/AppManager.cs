using System;

namespace MyBya.Singletons;

public class AppManager
{
    private static AppManager? instance;
    public static AppManager Instance
    {
        get => instance ??= new AppManager();
    }

    public async Task<bool> IsInternetConnected()
    {
        using HttpClient client = new();
        client.Timeout = new TimeSpan(0, 0, 0, 5);

        try
        {
            HttpResponseMessage response = await client
                .GetAsync("https://www.google.com");

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            return false;
        }
    }
}
