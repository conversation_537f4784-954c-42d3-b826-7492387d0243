﻿using CommunityToolkit.Maui;
using Microsoft.Extensions.Logging;
using MyBya.Configuration;
using MyBya.DbContext.Handler;
using MyBya.DbContext.Handler.Interface;
using MyBya.Extensions;
using MyBya.Helpers;

namespace MyBya;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		return Builder();
	}

	private static MauiApp Builder()
	{
		var builder = AppMauiContext.Instance.Builder;

		builder
			.UseMauiApp<App>()
			.UseMauiCommunityToolkit()
			.UseMauiCommunityToolkitMediaElement()
			.AddDependencies()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});
#if DEBUG
		builder.Logging.AddDebug();
#endif
		return builder.Build();
	}
}