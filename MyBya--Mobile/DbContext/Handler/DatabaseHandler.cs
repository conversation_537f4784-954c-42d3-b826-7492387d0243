using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Entities.Common;
using MyBya.Models.Common;
using Serilog;
using SQLite;

namespace MyBya.DbContext.Handler;

public class DatabaseHandler<E> : IDatabaseHandler<E> where E : BaseEntity, new()
{
    private object _lock = new object();
    public static bool isInitialized = false;

    private async Task<SQLiteAsyncConnection> InitDatabase()
    {
        return await CreateTables();
    }

    public async Task<SQLiteAsyncConnection> CreateConnection()
    {
        return await InitDatabase();
    }

    private async Task<SQLiteAsyncConnection> CreateTables()
    {
        IList<Type> tableTypes = new List<Type>
        {
            typeof(TestResultHeartRatesEntity),
            typeof(TestResultPaceChartEntity)
        };

        var connectionString = new SQLiteConnectionString(
            databasePath: DatabaseConstants.DatabasePath
        );

        var connection = new SQLiteAsyncConnection(connectionString);

        await connection?.CreateTablesAsync(
            CreateFlags.None,
            tableTypes.ToArray()
        );
        
        return connection;
    }

    public async Task Delete(E entity)
    {
        try
        {
            var connection = await CreateConnection();
            await connection.DeleteAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task Insert(E entity)
    {
        try
        {
            var connection = await CreateConnection();
            await connection.InsertAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task InsertAll(List<E> entities)
    {
        try
        {
            var connection = await CreateConnection();
            await connection.RunInTransactionAsync((conn) => conn.InsertAll(entities));
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<IList<E>> SelectAllItems()
    {
        try
        {
            var connection = await CreateConnection();

            var result = await connection
                .Table<E>()
                .ToListAsync();

            await connection.CloseAsync();
            return result;
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<IList<E>> SelectByCriteria(Expression<Func<E, bool>> predicate)
    {
        try
        {
            var connection = await CreateConnection();

            var result = await connection
                .Table<E>()
                .Where(predicate)
                .ToListAsync();

            await connection.CloseAsync();

            return result;
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task Update(E entity)
    {
        try
        {
            var connection = await CreateConnection();
            int execution = await connection.UpdateAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }

    }

    public async Task Clear()
    {
        try
        {
            var connection = await CreateConnection();
            await connection.RunInTransactionAsync((conn) => conn.DeleteAll<E>());
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<List<E>> ExecuteQuery(string query, params object[] values)
    {
        var connection = await CreateConnection();
        var result = await connection.ExecuteScalarAsync<List<E>>(query, values);
        await connection.CloseAsync();
        return result;
    }
}
