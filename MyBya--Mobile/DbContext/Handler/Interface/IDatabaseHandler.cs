using System;
using System.Linq.Expressions;
using MyBya.Entities.Common;
using MyBya.Models.Common;
using SQLite;

namespace MyBya.DbContext.Handler.Interface;

public interface IDatabaseHandler<E> where E : BaseEntity, new()
{
    Task<SQLiteAsyncConnection> CreateConnection();
    Task Delete(E entity);
    Task Insert(E entity);
    Task InsertAll(List<E> entities);
    Task<IList<E>> SelectAllItems();
    Task<IList<E>> SelectByCriteria(Expression<Func<E, bool>> predicate);
    Task Update(E entity);
    Task Clear();
    Task<List<E>> ExecuteQuery(string query, params object[] values);
}