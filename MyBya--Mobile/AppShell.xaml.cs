using MyBya.Ui.Pages;
using MyBya.Ui.Pages.TestProtocol;

namespace MyBya;

public partial class AppShell : Shell
{
    public AppShell()
    {
        InitializeComponent();
        
        // Register routes for navigation
        Routing.RegisterRoute("HomePage", typeof(HomePage));
        Routing.RegisterRoute("TestProtocolPage", typeof(TestProtocolInitialCreationPage));
        Routing.RegisterRoute("CalendarPage", typeof(TrainingSchedulePage));
        Routing.RegisterRoute("HistoryPage", typeof(WorkoutHistoryPage));
        Routing.RegisterRoute("AccountPage", typeof(AccountPage));
    }
}
