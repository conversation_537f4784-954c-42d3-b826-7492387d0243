<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="MyBya.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MyBya.Ui.Pages"
    xmlns:testprotocol="clr-namespace:MyBya.Ui.Pages.TestProtocol"
    Title="MyBya">

    <TabBar>
        <!-- Home Tab -->
        <ShellContent
            Title="Home"
            ContentTemplate="{DataTemplate local:HomePage}"
            Route="HomePage" />

        <!-- Initiate Test Protocol Tab -->
        <ShellContent
            Title="Test"
            ContentTemplate="{DataTemplate testprotocol:TestProtocolInitialCreationPage}"
            Route="TestProtocolPage" />

        <!-- Workout Calendar Tab -->
        <ShellContent
            Title="Calendar"
            ContentTemplate="{DataTemplate local:TrainingSchedulePage}"
            Route="CalendarPage" />

        <!-- Workout History Tab -->
        <ShellContent
            Title="History"
            ContentTemplate="{DataTemplate local:WorkoutHistoryPage}"
            Route="HistoryPage" />

        <!-- Account Tab -->
        <ShellContent
            Title="Account"
            ContentTemplate="{DataTemplate local:AccountPage}"
            Route="AccountPage" />
    </TabBar>

</Shell>
